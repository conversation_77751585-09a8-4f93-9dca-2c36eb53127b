# Maven
target/
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# IDE
.idea/
*.iml
*.ipr
*.iws
.vscode/
.classpath
.project
.settings/

# Logs
*.log
logs/

.chart/
.mvn/
.vercel/
.vscode/

# Docker & Build Files
*.pid
*.seed
*.pid.lock

# Docker 凭据文件 (安全保护)
docker-credentials.env
~/.docker-credentials
.docker-credentials
*-credentials.env
*.credentials

# Maven 本地仓库（可选）
.m2/

# 操作系统
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
*~

# 构建产物
build/
dist/
out/

ml_code/