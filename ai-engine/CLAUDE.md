# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

AI发票（AIFaPiao）是一个基于Spring Boot和Spring Cloud构建的微服务架构发票处理系统。系统处理发票识别、分类、邮件解析和企业信息管理等功能。

## 常用开发命令

### 构建命令
```bash
# 构建所有模块（跳过测试）
mvn clean install -Dmaven.test.skip=true -T 4 -s ./settings.xml

# 使用Jib构建单个服务（Docker镜像）
./build-with-jib.sh [服务名]

# 构建并推送到镜像仓库
./build-with-jib.sh -p [服务名]

# 构建所有服务
./build-with-jib.sh -a

# 使用指定标签构建
./build-with-jib.sh -t [标签名] [服务名]
```

### 运行测试
```bash
# 运行所有测试
mvn test

# 运行特定模块的测试
mvn test -pl [模块名]

# 运行单个测试类
mvn test -Dtest=类名

# 运行单个测试方法
mvn test -Dtest=类名#方法名
```

### 本地开发
```bash
# 本地启动服务（需要spring.profiles.active=dev-local）
mvn spring-boot:run -Dspring.profiles.active=dev-local -pl [模块名]

# 示例：启动datasource服务
cd datasource
mvn spring-boot:run -Dspring.profiles.active=dev-local
```

## 架构概览

### 服务架构
系统采用Spring Cloud微服务架构（使用Nacos作为服务发现和配置中心）：

- **核心服务**：Analysis、Classify - 基础设施服务
- **数据服务**：Datasource、Email-parser、PDF-parser、Enterprise-info - 数据处理
- **应用服务**：Email、Message、Scheduler、Card-import、SMS - 业务逻辑
- **Web服务**：TOC-web - 前端服务
- **客户端服务**：Didi-client、EKB-client、Mini-program - 客户端应用

### 技术栈
- **Java 11** - 核心语言
- **Spring Boot 2.2.2** - 应用框架
- **Spring Cloud Hoxton** - 微服务框架
- **Nacos 2.3.2** - 服务发现和配置管理
- **Maven** - 构建工具，多模块结构
- **Jib** - 无需Docker守护进程的镜像构建
- **MyBatis** - 数据库ORM
- **RabbitMQ** - 消息队列
- **Redis** - 缓存
- **阿里云OSS** - 对象存储

### 模块结构
```
ai-engine/
├── common/          # 共享的DTO、工具类、枚举
├── oss/            # 阿里云OSS集成
├── image/          # 图像处理（公司图标主题色提取）
├── analysis/       # 文本分析和公司名称提取
├── classify/       # AI发票分类
├── datasource/     # 数据持久层
├── email/          # 邮件收发服务
├── email-parser/   # 邮件内容解析
├── pdf-parser/     # PDF发票解析
├── enterprise-info/# 企业信息查询（企查查/天眼查）
├── message/        # 消息服务
├── scheduler/      # 任务调度
├── card-import/    # 支付宝卡片导入
├── sms/           # 短信服务
├── client/        # 客户端应用
│   ├── didi-client/
│   ├── ekb-client/
│   └── mini-program/
└── toc-web/       # Web界面
```

### 关键设计模式

1. **服务通信**：使用Feign客户端的RESTful API
2. **配置管理**：通过Nacos Config外部化配置，支持环境特定配置
3. **数据传输**：`common`模块中的通用DTO在服务间共享
4. **错误处理**：每个服务中的集中式异常处理
5. **认证机制**：基于令牌的认证，带有账户用户管理

### 数据库相关
- 服务使用MyBatis进行数据库操作
- Mapper XML文件位于`src/main/resources/mapper/`
- 通用实体定义在`common/src/main/java/com/guoranbot/common/po/`

### Docker与部署
- 使用Jib进行无容器的Docker构建
- 基础镜像：`ekb-repo.tencentcloudcr.com/library/runtime-openjdk:11`
- 使用Datadog代理进行APM监控
- Kubernetes部署配置在`deploy/`目录

### 开发注意事项
- 文件操作始终使用绝对路径
- 检查Maven仓库认证（repo.ekuaibao.com需要凭证）
- 使用环境变量管理敏感配置
- 本地开发运行时使用`-Dspring.profiles.active=dev-local`
- 服务默认端口8080，可通过SERVER_PORT环境变量覆盖