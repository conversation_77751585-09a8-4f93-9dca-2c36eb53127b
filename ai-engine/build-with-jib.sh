#!/bin/bash

# Jib构建脚本 - 支持单个服务或批量构建
# 用法：
#   ./build-with-jib.sh [服务名]              # 构建单个服务到本地
#   ./build-with-jib.sh -p [服务名]           # 构建并推送单个服务
#   ./build-with-jib.sh -a                   # 构建所有服务到本地  
#   ./build-with-jib.sh -ap                  # 构建并推送所有服务
#   ./build-with-jib.sh -t tag_name service  # 使用指定标签构建服务
#   ./build-with-jib.sh --clean service      # 清理缓存后构建服务
#
# Docker认证环境变量（参考build-image.sh）：
#   DOCKER_REGISTRY_USERNAME  - Docker仓库用户名（默认：100012303395）
#   DOCKER_REGISTRY_PASSWORD  - Docker仓库密码
#   DOCKER_REGISTRY_TOKEN     - Docker仓库token（作为密码的替代）
#
# 示例:
#   export DOCKER_REGISTRY_USERNAME="100012303395"
#   export DOCKER_REGISTRY_PASSWORD="your_password"
#   ./build-with-jib.sh -p eureka
#
#   或使用token:
#   export DOCKER_REGISTRY_TOKEN="your_token"
#   ./build-with-jib.sh -ap

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 性能优化配置
PARALLEL_JOBS=${PARALLEL_JOBS:-$(nproc 2>/dev/null || echo 4)}
MAVEN_OPTS="${MAVEN_OPTS} -Xmx4g -XX:+UseG1GC -XX:+UseStringDeduplication"
JIB_CACHE_DIR=${JIB_CACHE_DIR:-"/tmp/jib-cache"}
DOCKER_BUILDKIT=1
BUILD_MODE="${BUILD_MODE:-optimized}"

# 性能监控（兼容旧版bash）
BUILD_TIMES_FILE="/tmp/jib_build_times.txt"
BUILD_STATUS_FILE="/tmp/jib_build_status.txt"
# 清理旧的临时文件
rm -f "$BUILD_TIMES_FILE" "$BUILD_STATUS_FILE"
touch "$BUILD_TIMES_FILE" "$BUILD_STATUS_FILE"

# 检查Maven仓库认证
check_maven_auth() {
    if [[ -z "$MAVEN_REPO_USER" && -z "$MAVEN_REPO_PASS" ]]; then
        echo -e "${YELLOW}⚠️  检测到Maven仓库认证缺失${NC}"
        echo -e "${YELLOW}私有仓库 repo.ekuaibao.com 需要认证信息${NC}"
        echo ""
        echo -e "${BLUE}解决方案1 - 设置环境变量:${NC}"
        echo -e "${GREEN}export MAVEN_REPO_USER=\"your_username\"${NC}"
        echo -e "${GREEN}export MAVEN_REPO_PASS=\"your_password\"${NC}"
        echo ""
        echo -e "${BLUE}解决方案2 - 本地构建策略:${NC}"
        echo -e "${GREEN}将首先构建common等基础模块到本地仓库${NC}"
        echo ""
        return 1
    else
        echo -e "${GREEN}✅ Maven仓库认证信息已设置${NC}"
        return 0
    fi
}

# 构建本地依赖模块 - 严格按照 build-image.sh 的阶段2.1流程
build_local_dependencies() {
    echo -e "${BLUE}=== 阶段2.1: 构建库模块（与build-image.sh保持一致）===${NC}"
    
    # 基础依赖模块列表（与build-image.sh中的LIBRARY_MODULES保持一致）
    local LIBRARY_MODULES=("common" "oss" "image")
    
    # 获取settings.xml文件
    local settings_file=$(find_settings_xml)
    local settings_param=""
    if [[ -n "$settings_file" ]]; then
        settings_param="--settings $settings_file"
    fi
    
    echo -e "${YELLOW}处理库模块: ${LIBRARY_MODULES[*]}${NC}"
    
    for module in "${LIBRARY_MODULES[@]}"; do
        if [[ -d "$module" ]]; then
            echo -e "${BLUE}打包库模块: $module${NC}"
            cd "$module"
            
            # 使用与build-image.sh相同的Maven参数
            # 注意：库模块只需要普通的package，不需要spring-boot:repackage
            local maven_cmd="mvn package -Dmaven.test.skip=true -DskipTests=true $settings_param --batch-mode"
            maven_cmd="$maven_cmd -Dmaven.wagon.http.retryHandler.count=3"
            maven_cmd="$maven_cmd -Dmaven.wagon.httpconnectionManager.ttlSeconds=120"
            maven_cmd="$maven_cmd -Dmaven.test.skip.exec=true"
            
            echo -e "${GREEN}执行: $maven_cmd${NC}"
            
            # 尝试离线构建，失败则在线构建
            if eval "$maven_cmd --offline" 2>/dev/null || eval "$maven_cmd"; then
                echo -e "${GREEN}✅ 库模块 $module 打包成功${NC}"
                
                # 验证jar文件是否生成
                local jar_file="target/${module}-0.0.1-SNAPSHOT.jar"
                if [[ -f "$jar_file" ]]; then
                    echo -e "${GREEN}   ✓ $jar_file 已生成${NC}"
                else
                    echo -e "${YELLOW}   ⚠ $jar_file 未找到，但构建报告成功${NC}"
                fi
            else
                echo -e "${RED}❌ 库模块 $module 打包失败${NC}"
                echo -e "${YELLOW}💡 如果是依赖下载失败，尝试:${NC}"
                echo -e "${YELLOW}   1. 运行修复脚本: ./fix-maven-deps.sh${NC}"
                echo -e "${YELLOW}   2. 清理测试依赖: rm -rf ~/.m2/repository/org/junit${NC}"
                echo -e "${YELLOW}   3. 手动构建: mvn clean package -Dmaven.test.skip=true -f $module/pom.xml${NC}"
                echo -e "${YELLOW}   注意：库模块构建失败可能影响应用模块的构建${NC}"
            fi
            
            cd ..
        else
            echo -e "${YELLOW}⚠ 模块目录不存在: $module${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ 库模块构建阶段完成${NC}"
}

# 所有需要构建Docker镜像的服务列表
# 严格按照 build-image.sh 中的构建顺序排列，确保依赖关系正确
# 排除的服务（与 build-image.sh 保持一致）：
# - 库模块: common, oss, image (依赖库，不需要独立部署)
# - 聚合模块: client (只是子模块的聚合器，不是应用服务)
# - Client子模块: client/* (未配置Jib，在build-image.sh中使用Dockerfile构建)

# 构建顺序说明：
# 1. 首先构建核心服务（analysis, classify, eureka）
# 2. 然后构建数据服务（datasource, email-parser, pdf-parser, enterprise-info）  
# 3. 再构建应用服务（email, message, scheduler等）
# 4. 最后构建Web服务（toc-web）

ALL_SERVICES=(
    # 🏗️ 核心服务组 - 基础设施服务，其他服务可能依赖它们
    "analysis"
    "classify"
    
    # 📊 数据服务组 - 数据处理和解析服务
    "datasource"
    "email-parser"
    "pdf-parser"
    "enterprise-info"
    
    # 🚀 应用服务组 - 业务逻辑服务
    "email"
    "message"
    "scheduler"
    "card-import"
    "sms"
    "dasource-listen"
    "customize-email-datasource"
    
    # 🌐 Web服务组 - 前端服务
    "toc-web"

    # 📱 客户端服务组 - Jib构建
    "client/didi-client"
    "client/ekb-client"
    "client/mini-program"
)

# 默认参数
PUSH_MODE=false
BUILD_ALL=false
CLEAN_CACHE=false
SKIP_TESTS=true
SKIP_TESTS_COMPILE=false  # 是否跳过测试编译
DOCKER_TAG=""
TARGET_SERVICE=""
SETTINGS_FILE=""  # 添加settings.xml文件路径参数
FAST_MODE=false
PARALLEL_MODE=false

# 显示使用说明
show_usage() {
    echo -e "${BLUE}Jib构建脚本用法:${NC}"
    echo ""
    echo -e "${YELLOW}单个服务构建:${NC}"
    echo "  $0 [服务名]                    # 构建到本地"
    echo "  $0 -p [服务名]                 # 构建并推送"
    echo "  $0 -t [标签] [服务名]          # 使用指定标签构建"
    echo "  $0 -pt [标签] [服务名]         # 使用指定标签构建并推送"
    echo ""
    echo -e "${YELLOW}批量构建:${NC}"
    echo "  $0 -a                         # 构建所有服务到本地"
    echo "  $0 -ap                        # 构建并推送所有服务"
    echo "  $0 -at [标签]                 # 使用指定标签构建所有服务"
    echo "  $0 -apt [标签]                # 使用指定标签构建并推送所有服务"
    echo ""
    echo -e "${YELLOW}性能优化选项:${NC}"
    echo "  $0 --fast [服务名]            # 启用快速构建模式"
    echo "  $0 --parallel [N]             # 使用N个并行作业（默认: CPU核心数）"
    echo "  $0 --cache-dir [路径]         # 指定Jib缓存目录"
    echo "  $0 --clean [服务名]           # 清理缓存后构建"
    echo ""
    echo -e "${YELLOW}其他选项:${NC}"
    echo "  $0 --test [服务名]            # 包含测试的构建"
    echo "  $0 --skip-tests [服务名]      # 跳过测试执行但编译测试代码（默认）"
    echo "  $0 --skip-all-tests [服务名]  # 完全跳过测试（不编译不执行）"
    echo "  $0 --settings [path] [服务名] # 指定settings.xml文件路径"
    echo "  $0 -h, --help                # 显示此帮助信息"
    echo ""
    echo -e "${YELLOW}可用服务:${NC}"
    printf "  "
    for service in "${ALL_SERVICES[@]}"; do
        printf "${service} "
    done
    echo ""
    echo ""
    echo -e "${YELLOW}排除的服务（与build-image.sh保持一致）:${NC}"
    echo -e "${YELLOW}  库模块: common, oss, image (依赖库，不独立部署)${NC}"
    echo ""
    echo -e "${YELLOW}镜像标签说明:${NC}"
    echo "  如果不指定-t参数，默认使用当前git分支名作为标签"
    echo "  每次构建都会同时生成latest和项目版本号标签"
    echo ""
    echo -e "${YELLOW}测试执行选项:${NC}"
    echo "  默认行为: 跳过测试执行但编译测试代码（-DskipTests）"
    echo "  --test: 运行完整测试"
    echo "  --skip-tests: 跳过测试执行但编译测试代码（默认）"
    echo "  --skip-all-tests: 完全跳过测试，不编译也不执行（-Dmaven.test.skip=true）"
    echo ""
    echo -e "${YELLOW}Settings.xml文件查找顺序（默认使用同级目录）:${NC}"
    echo "  🎯 默认行为: 优先使用项目根目录的 ./settings.xml"
    echo "  1. --settings参数指定的文件（如果提供）"
    echo "  2. 项目根目录的 ./settings.xml（默认首选）"
    echo "  3. ~/.m2/settings.xml（Maven默认位置）"
    echo "  4. 如果都不存在，使用Maven默认配置"
    echo ""
    echo -e "${YELLOW}构建流程说明（严格按照build-image.sh顺序）:${NC}"
    echo "  📋 阶段0: 清理和准备 - 检查认证信息"
    echo "  🔍 阶段1: 检查构建环境 - 验证Maven配置"  
    echo "  🏗️ 阶段2: 构建依赖模块 - 先构建库模块(common,oss,image)"
    echo "  🐳 阶段3: Jib Docker镜像构建 - 按依赖顺序构建应用服务"
    echo "     └── 每个服务: 预编译+打包 → Jib镜像构建"
    echo ""
}

# 获取当前git分支
get_current_branch() {
    git branch --show-current 2>/dev/null || echo "develop"
}

# 验证服务名是否有效
validate_service() {
    local service=$1
    for valid_service in "${ALL_SERVICES[@]}"; do
        if [[ "$service" == "$valid_service" ]]; then
            return 0
        fi
    done
    echo -e "${RED}错误: 无效的服务名 '$service'${NC}"
    echo -e "${YELLOW}可用服务: ${ALL_SERVICES[*]}${NC}"
    exit 1
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -a|--all)
                BUILD_ALL=true
                shift
                ;;
            -p|--push)
                PUSH_MODE=true
                shift
                ;;
            -t|--tag)
                if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                    DOCKER_TAG="$2"
                    shift 2
                else
                    echo -e "${RED}错误: -t/--tag 参数需要指定标签值${NC}"
                    exit 1
                fi
                ;;
            -pt|-tp)
                PUSH_MODE=true
                if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                    DOCKER_TAG="$2"
                    shift 2
                else
                    echo -e "${RED}错误: -pt 参数需要指定标签值${NC}"
                    exit 1
                fi
                ;;
            -ap|-pa)
                BUILD_ALL=true
                PUSH_MODE=true
                shift
                ;;
            -at|-ta)
                BUILD_ALL=true
                if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                    DOCKER_TAG="$2"
                    shift 2
                else
                    echo -e "${RED}错误: -at 参数需要指定标签值${NC}"
                    exit 1
                fi
                ;;
            -apt|-atp|-pat|-pta|-tap|-tpa)
                BUILD_ALL=true
                PUSH_MODE=true
                if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                    DOCKER_TAG="$2"
                    shift 2
                else
                    echo -e "${RED}错误: -apt 参数需要指定标签值${NC}"
                    exit 1
                fi
                ;;
            --clean)
                CLEAN_CACHE=true
                shift
                ;;
            --test)
                SKIP_TESTS=false
                SKIP_TESTS_COMPILE=false
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                SKIP_TESTS_COMPILE=false
                shift
                ;;
            --skip-all-tests)
                SKIP_TESTS=true
                SKIP_TESTS_COMPILE=true
                shift
                ;;
            --settings)
                if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                    SETTINGS_FILE="$2"
                    shift 2
                else
                    echo -e "${RED}错误: --settings 参数需要指定settings.xml文件路径${NC}"
                    exit 1
                fi
                ;;
            --fast)
                FAST_MODE=true
                shift
                ;;
            --parallel)
                if [[ -n "$2" && "$2" =~ ^[0-9]+$ ]]; then
                    PARALLEL_JOBS=$2
                    PARALLEL_MODE=true
                    shift 2
                else
                    PARALLEL_JOBS=$(nproc 2>/dev/null || echo 4)
                    PARALLEL_MODE=true
                    shift
                fi
                ;;
            --cache-dir)
                if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                    JIB_CACHE_DIR="$2"
                    shift 2
                else
                    echo -e "${RED}错误: --cache-dir 参数需要指定缓存目录${NC}"
                    exit 1
                fi
                ;;
            -*)
                echo -e "${RED}错误: 未知参数 $1${NC}"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$TARGET_SERVICE" ]]; then
                    TARGET_SERVICE="$1"
                else
                    echo -e "${RED}错误: 只能指定一个目标服务${NC}"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 查找settings.xml文件 - 默认优先使用项目根目录（同级目录）的settings.xml
find_settings_xml() {
    # 优先级顺序：
    # 1. 命令行--settings参数指定的文件（如果提供）
    # 2. 项目根目录的settings.xml（默认首选）
    # 3. ~/.m2/settings.xml（Maven默认位置）
    
    # 只有当明确指定--settings参数时才使用指定文件
    if [[ -n "$SETTINGS_FILE" ]]; then
        if [[ -f "$SETTINGS_FILE" ]]; then
            echo "$SETTINGS_FILE"
            return 0
        else
            echo -e "${RED}错误: 指定的settings.xml文件不存在: $SETTINGS_FILE${NC}" >&2
            return 1
        fi
    fi
    
    # 默认行为：优先使用项目根目录（同级目录）的settings.xml
    if [[ -f "./settings.xml" ]]; then
        echo "./settings.xml"
        return 0
    fi
    
    # 备选方案：使用Maven默认位置的settings.xml
    if [[ -f "$HOME/.m2/settings.xml" ]]; then
        echo "$HOME/.m2/settings.xml"
        return 0
    fi
    
    # 没有找到任何settings.xml文件
    echo ""
    return 0
}

# 初始化缓存
init_cache() {
    echo -e "${BLUE}初始化构建缓存...${NC}"
    
    # 创建Jib缓存目录
    mkdir -p "$JIB_CACHE_DIR"
    
    # 预拉取基础镜像（后台执行）
    if command -v docker &> /dev/null; then
        echo -e "${YELLOW}预拉取基础镜像...${NC}"
        docker pull ekb-repo.tencentcloudcr.com/library/runtime-openjdk:11 >/dev/null 2>&1 &
    fi
    
    # Maven依赖预下载（后台执行）
    if [[ "$FAST_MODE" == "true" ]]; then
        echo -e "${YELLOW}预下载Maven依赖...${NC}"
        mvn dependency:go-offline -q -Dmaven.test.skip=true >/dev/null 2>&1 &
    fi
    
    wait
}

# 智能缓存清理
smart_cache_clean() {
    echo -e "${BLUE}智能缓存清理...${NC}"

    # 清理旧的Jib缓存
    find "$JIB_CACHE_DIR" -type f -mtime +7 -delete 2>/dev/null || true

    # 清理Maven本地缓存（保留最近7天）
    find ~/.m2/repository -name "*.lastUpdated" -mtime +1 -delete 2>/dev/null || true

    # Docker系统清理（保留最近镜像）
    docker system prune -f --filter "until=24h" 2>/dev/null || true
}

# 记录推送到仓库后的镜像信息
log_pushed_image_info() {
    local service=$1
    local mode=$2
    local service_name
    service_name=$(basename "$service")

    # 只在推送模式下显示镜像信息
    if [[ "$mode" != "push" ]]; then
        return 0
    fi

    # 从Maven配置中获取镜像信息
    local docker_repository="ekb-repo.tencentcloudcr.com/xbox"
    # 根据实际的Maven配置确定镜像名称
    local image_name
    case "$service_name" in
        "analysis") image_name="analysis-service" ;;
        "classify") image_name="classify-service" ;;
        "email") image_name="email-service" ;;
        "email-parser") image_name="email-parser-service" ;;
        "pdf-parser") image_name="pdf-parser-service" ;;
        "message") image_name="message-service" ;;
        "scheduler") image_name="scheduler-service" ;;
        "sms") image_name="sms-service" ;;
        "datasource") image_name="data-service" ;;
        "enterprise-info") image_name="enterprise-info" ;;
        "card-import") image_name="card-import-service" ;;
        "toc-web") image_name="toc-web" ;;
        "dasource-listen") image_name="dasource-listen-service" ;;
        "customize-email-datasource") image_name="customize-email-data-service" ;;
        "mini-program") image_name="mini-program-service" ;;
        "didi-client") image_name="didi-client-service" ;;
        "ekb-client") image_name="ekb-client-service" ;;
        *) image_name="${service_name}-service" ;;
    esac
    local current_tag="$DOCKER_TAG"

    # 如果没有指定标签，使用当前git分支
    if [[ -z "$current_tag" ]]; then
        current_tag=$(get_current_branch)
    fi

    # 尝试从pom.xml中提取项目版本，如果失败则使用默认值
    local project_version="0.0.1-SNAPSHOT"
    if [[ -f "pom.xml" ]]; then
        local extracted_version
        extracted_version=$(grep -m1 "<version>" pom.xml | sed 's/.*<version>\(.*\)<\/version>.*/\1/' | tr -d ' ' 2>/dev/null || echo "")
        if [[ -n "$extracted_version" && "$extracted_version" != *"{"* ]]; then
            project_version="$extracted_version"
        fi
    fi

    echo ""
    echo -e "${GREEN}📦 镜像推送成功:${NC}"
    echo -e "${BLUE}  仓库: ${docker_repository}${NC}"
    echo -e "${BLUE}  镜像: ${image_name}${NC}"
    echo -e "${BLUE}  标签: latest, ${current_tag}, ${project_version}${NC}"
    echo -e "${YELLOW}  完整地址:${NC}"
    echo -e "${GREEN}    - ${docker_repository}/${image_name}:latest${NC}"
    echo -e "${GREEN}    - ${docker_repository}/${image_name}:${current_tag}${NC}"
    echo -e "${GREEN}    - ${docker_repository}/${image_name}:${project_version}${NC}"
    echo ""
    echo -e "${YELLOW}💡 提示: 使用JAVA_TOOL_OPTIONS环境变量在Kubernetes中动态配置JVM参数${NC}"
    echo -e "${BLUE}   示例: JAVA_TOOL_OPTIONS=\"-Xms512m -Xmx1024m -XX:+UseG1GC\"${NC}"
    echo ""
}

# 构建单个服务（优化版）
build_service() {
    local service=$1
    local mode=$2  # "local" 或 "push"
    local service_name
    service_name=$(basename "$service")
    local start_time=$(date +%s)
    
    echo -e "${BLUE}🚀 开始构建服务: ${service} (模块: ${service_name})${NC}"
    
    if [[ ! -d "$service" ]]; then
        echo -e "${RED}错误: 服务目录 '$service' 不存在${NC}"
        return 1
    fi
    
    cd "$service"
    
    # 设置Maven选项
    export MAVEN_OPTS="$MAVEN_OPTS"
    
    # 构建Maven命令（优化版）
    local maven_cmd="mvn"
    local jib_goal
    local maven_opts=""
    
    # 设置镜像标签
    local tag_param=""
    if [[ -n "$DOCKER_TAG" ]]; then
        tag_param="-Ddocker.tag=$DOCKER_TAG"
    fi
    
    # 确定Jib目标
    if [[ "$mode" == "push" ]]; then
        jib_goal="jib:build"
    else
        jib_goal="jib:dockerBuild"
    fi
    
    # 添加测试跳过选项
    if [[ "$SKIP_TESTS" == "true" ]]; then
        if [[ "$SKIP_TESTS_COMPILE" == "true" ]]; then
            maven_opts="$maven_opts -Dmaven.test.skip=true"
        else
            maven_opts="$maven_opts -DskipTests"
        fi
    fi
    
    # 添加并行构建选项
    if [[ "$PARALLEL_MODE" == "true" ]]; then
        maven_opts="$maven_opts -T $PARALLEL_JOBS"
    fi
    
    # 添加Jib优化参数
    maven_opts="$maven_opts -Djib.useOnlyProjectCache=true"
    maven_opts="$maven_opts -Djib.baseImageCache=$JIB_CACHE_DIR"
    maven_opts="$maven_opts -Djib.applicationCache=$JIB_CACHE_DIR/app"
    maven_opts="$maven_opts -Djib.httpTimeout=30000"
    maven_opts="$maven_opts -Djib.allowInsecureRegistries=true"
    maven_opts="$maven_opts -Dmaven.wagon.http.retryHandler.count=3"
    maven_opts="$maven_opts -Dmaven.wagon.httpconnectionManager.ttlSeconds=120"
    
    # 设置settings.xml文件
    local settings_file_param=""
    local settings_file=$(find_settings_xml)
    if [[ -n "$settings_file" ]]; then
        settings_file_param="--settings $settings_file"
    fi
    
    # 清理缓存
    if [[ "$CLEAN_CACHE" == "true" ]]; then
        echo -e "${YELLOW}清理缓存...${NC}"
        smart_cache_clean
    fi
    
    # 优化构建流程
    echo -e "${BLUE}执行优化构建流程...${NC}"
    
    # 构建命令（预编译 + Jib构建合并）
    local full_build_cmd="$maven_cmd clean package $jib_goal $maven_opts $tag_param $settings_file_param --batch-mode"
    
    echo -e "${GREEN}执行: $full_build_cmd${NC}"
    
    if eval "$full_build_cmd"; then
        echo "$service: SUCCESS" >> "$BUILD_STATUS_FILE"
        echo -e "${GREEN}✅ 服务 $service 构建成功${NC}"

        # 记录推送到仓库后的镜像信息
        log_pushed_image_info "$service" "$mode"
    else
        echo "$service: FAILED" >> "$BUILD_STATUS_FILE"
        echo -e "${RED}❌ 服务 $service 构建失败${NC}"
        cd ..
        return 1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "$service: $duration" >> "$BUILD_TIMES_FILE"
    
    cd ..
    return 0
}

# 性能报告
show_performance_info() {
    echo -e "${BLUE}=== 性能优化配置 ===${NC}"
    echo -e "${GREEN}并行作业数: ${PARALLEL_JOBS}${NC}"
    echo -e "${GREEN}Jib缓存目录: ${JIB_CACHE_DIR}${NC}"
    echo -e "${GREEN}Maven内存: ${MAVEN_OPTS}${NC}"
    [[ "$FAST_MODE" == "true" ]] && echo -e "${GREEN}快速模式: 启用${NC}"
    [[ "$PARALLEL_MODE" == "true" ]] && echo -e "${GREEN}并行模式: 启用${NC}"
    echo ""
}

# 性能报告
generate_performance_report() {
    echo -e "${BLUE}=== 构建性能报告 ===${NC}"
    local total_time=0
    local success_count=0
    local total_services=0
    
    if [[ -f "$BUILD_TIMES_FILE" ]]; then
        while IFS= read -r line; do
            if [[ "$line" =~ ^([^:]+):[[:space:]]*(.*)$ ]]; then
                local service="${BASH_REMATCH[1]}"
                local time="${BASH_REMATCH[2]}"
                local status="FAILED"
                
                # 从状态文件中获取状态
                if [[ -f "$BUILD_STATUS_FILE" ]]; then
                    status=$(grep "^$service:" "$BUILD_STATUS_FILE" | cut -d: -f2- | tr -d ' ' || echo "UNKNOWN")
                fi
                
                if [[ "$status" == "SUCCESS" ]]; then
                    success_count=$((success_count + 1))
                fi
                
                echo -e "  ${service}: ${time}s [${status}]"
                total_time=$((total_time + time))
                total_services=$((total_services + 1))
            fi
        done < "$BUILD_TIMES_FILE"
    fi
    
    if [[ $total_services -eq 0 ]]; then
        echo -e "${YELLOW}没有构建任何服务${NC}"
    else
        echo -e "${GREEN}总构建时间: ${total_time}s${NC}"
        echo -e "${GREEN}成功服务: ${success_count}/${total_services}${NC}"
    fi
}

# 主执行函数（优化版）
main() {
    local start_time=$(date +%s)
    
    # 解析参数
    parse_arguments "$@"
    
    # 检查是否在项目根目录
    if [[ ! -f "pom.xml" ]]; then
        echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 显示性能信息
    show_performance_info
    
    # 初始化缓存
    init_cache
    
    # 设置默认标签
    if [[ -z "$DOCKER_TAG" ]]; then
        DOCKER_TAG=$(get_current_branch)
        echo -e "${BLUE}使用当前git分支作为镜像标签: ${DOCKER_TAG}${NC}"
    fi
    
    # 执行构建
    local failed_services=()
    
    echo -e "${BLUE}=== 开始优化构建 ===${NC}"
    
    if [[ "$BUILD_ALL" == "true" ]]; then
        echo -e "${BLUE}📊 批量构建所有服务（${PARALLEL_JOBS}并行）...${NC}"
        
        # 分阶段并行构建
        local phase1=("common" "oss" "image")
        local phase2=("eureka" "datasource" "email-parser" "pdf-parser" "enterprise-info")
        local phase3=("analysis" "classify" "email" "message" "scheduler")
        local phase4=("sms" "card-import" "dasource-listen" "customize-email-datasource")
        local phase5=("toc-web" "client/didi-client" "client/ekb-client" "client/mini-program")
        
        local all_phases=("phase1" "phase2" "phase3" "phase4" "phase5")
        
        for phase in "${all_phases[@]}"; do
            eval "local services=(\${$phase[@]})"
            echo -e "${YELLOW}构建阶段: ${services[*]}${NC}"
            
            for service in "${services[@]}"; do
                if [[ -d "$service" ]]; then
                    build_service "$service" "$( [[ "$PUSH_MODE" == "true" ]] && echo "push" || echo "local" )" || failed_services+=("$service")
                fi
            done
        done
    else
        if [[ -z "$TARGET_SERVICE" ]]; then
            echo -e "${RED}错误: 必须指定要构建的服务名${NC}"
            show_usage
            exit 1
        fi
        
        validate_service "$TARGET_SERVICE"
        build_service "$TARGET_SERVICE" "$( [[ "$PUSH_MODE" == "true" ]] && echo "push" || echo "local" )" || failed_services+=("$TARGET_SERVICE")
    fi
    
    # 显示结果
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    generate_performance_report
    echo -e "${BLUE}总耗时: ${duration}秒${NC}"
    
    [[ ${#failed_services[@]} -eq 0 ]] && exit 0 || exit 1
}

# 启动脚本
main "$@"