package com.guoranbot.datasource.controller;

import com.guoranbot.common.dto.EmailRecordDto;
import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.datasource.service.IAccountUserService;
import com.guoranbot.datasource.service.IEmailRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 邮箱清理相关接口
 */
@Api(tags = "数据源邮件清理接口", description = "提供邮件清理相关的数据查询和状态管理功能")
@RestController
@RequestMapping("/email/cleanup")
public class EmailCleanupController {

    @Autowired
    private IEmailRecordService emailRecordService;

    @Autowired
    private IAccountUserService accountUserService;

    /**
     * 获取成功解析的邮件（T+days清理）
     */
    @ApiOperation(value = "获取成功解析的邮件", notes = "获取指定天数前成功解析的邮件列表")
    @GetMapping("/success")
    public ResponseEntity<List<EmailRecordDto>> getSuccessEmailsForCleanup(@ApiParam(value = "天数", required = true) @RequestParam int days) {
        if (days <= 0) {
            return ResponseEntity.badRequest().build();
        }
        List<EmailRecordDto> emails = emailRecordService.getSuccessEmailsForCleanup(days);
        return ResponseEntity.ok(emails);
    }

    /**
     * 获取失败解析的邮件（T+days清理）
     */
    @ApiOperation(value = "获取失败解析的邮件", notes = "获取指定天数前失败解析的邮件列表")
    @GetMapping("/failed")
    public ResponseEntity<List<EmailRecordDto>> getFailedEmailsForCleanup(@ApiParam(value = "天数", required = true) @RequestParam int days) {
        if (days <= 0) {
            return ResponseEntity.badRequest().build();
        }
        List<EmailRecordDto> emails = emailRecordService.getFailedEmailsForCleanup(days);
        return ResponseEntity.ok(emails);
    }

    /**
     * 根据邮箱地址获取待清理邮件
     */
    @ApiOperation(value = "根据邮箱地址获取待清理邮件", notes = "根据邮箱地址和条件获取待清理的邮件列表")
    @GetMapping("/by-email")
    public ResponseEntity<List<EmailRecordDto>> getEmailsForCleanupByEmail(
            @ApiParam(value = "邮箱地址", required = true) @RequestParam String emailAddress,
            @ApiParam(value = "天数", required = true) @RequestParam int days,
            @ApiParam(value = "是否仅成功邮件", required = true) @RequestParam boolean isSuccessOnly) {
        if (emailAddress == null || emailAddress.trim().isEmpty() || days <= 0) {
            return ResponseEntity.badRequest().build();
        }
        List<EmailRecordDto> emails = emailRecordService.getEmailsForCleanupByEmail(emailAddress, days, isSuccessOnly);
        return ResponseEntity.ok(emails);
    }

    /**
     * 批量标记邮件为已清理
     */
    @ApiOperation(value = "批量标记邮件为已清理", notes = "根据邮件ID列表批量标记邮件为已清理状态")
    @PostMapping("/mark-cleared")
    public ResponseEntity<Integer> markEmailsAsCleared(@RequestBody List<Long> emailIds) {
        if (emailIds == null || emailIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        int count = emailRecordService.markEmailsAsCleared(emailIds);
        return ResponseEntity.ok(count);
    }

    /**
     * 根据UID批量标记邮件为已清理
     */
    @ApiOperation(value = "根据UID批量标记邮件为已清理", notes = "根据邮件UID列表批量标记邮件为已清理状态")
    @PostMapping("/mark-cleared-by-uid")
    public ResponseEntity<Integer> markEmailsAsClearedByUIDs(@RequestBody List<String> messageUIDs) {
        if (messageUIDs == null || messageUIDs.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        int count = emailRecordService.markEmailsAsClearedByUIDs(messageUIDs);
        return ResponseEntity.ok(count);
    }

    /**
     * 获取清理统计信息
     */
    @ApiOperation(value = "获取清理统计信息", notes = "获取指定天数内可清理的邮件统计信息")
    @GetMapping("/statistics")
    public ResponseEntity<Integer> getCleanupStatistics(@ApiParam(value = "天数", required = true) @RequestParam int days) {
        if (days <= 0) {
            return ResponseEntity.badRequest().build();
        }
        int count = emailRecordService.getCleanupStatistics(days);
        return ResponseEntity.ok(count);
    }

    /**
     * 批量标记邮件为已清理（支持分批处理）
     */
    @ApiOperation(value = "批量标记邮件为已清理（分批处理）", notes = "支持分批处理的批量标记邮件为已清理状态")
    @PostMapping("/batch-mark-cleared")
    public ResponseEntity<Integer> batchMarkEmailsAsCleared(
            @RequestBody List<Long> emailIds,
            @ApiParam(value = "批次大小", defaultValue = "100") @RequestParam(defaultValue = "100") int batchSize) {
        if (emailIds == null || emailIds.isEmpty() || batchSize <= 0) {
            return ResponseEntity.badRequest().build();
        }
        int count = emailRecordService.batchMarkEmailsAsCleared(emailIds, batchSize);
        return ResponseEntity.ok(count);
    }

    /**
     * 获取已清理的邮件
     */
    @ApiOperation(value = "获取已清理的邮件", notes = "获取指定天数内已清理的邮件列表")
    @GetMapping("/already-cleared")
    public ResponseEntity<List<EmailRecordDto>> getAlreadyClearedEmails(@ApiParam(value = "天数", required = true) @RequestParam int days) {
        if (days <= 0) {
            return ResponseEntity.badRequest().build();
        }
        List<EmailRecordDto> emails = emailRecordService.getAlreadyClearedEmails(days);
        return ResponseEntity.ok(emails);
    }

    /**
     * 恢复邮件状态（取消已清理标记）
     */
    @ApiOperation(value = "恢复邮件状态", notes = "取消邮件的已清理标记，恢复到原始状态")
    @PostMapping("/restore-status")
    public ResponseEntity<Integer> restoreEmailStatus(@RequestBody List<Long> emailIds) {
        if (emailIds == null || emailIds.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        int count = emailRecordService.restoreEmailStatus(emailIds);
        return ResponseEntity.ok(count);
    }

    /**
     * 获取所有AIFAPIAO邮箱账户
     */
    @ApiOperation(value = "获取所有AIFAPIAO邮箱账户", notes = "获取系统中所有@aifapiao.com邮箱账户信息，用于邮件清理服务")
    @GetMapping("/aifapiao-accounts")
    public ResponseEntity<List<AccountUserEmail>> getAifapiaoEmailAccounts() {
        List<AccountUserEmail> accounts = accountUserService.getAifapiaoEmailAccounts();
        return ResponseEntity.ok(accounts);
    }
}