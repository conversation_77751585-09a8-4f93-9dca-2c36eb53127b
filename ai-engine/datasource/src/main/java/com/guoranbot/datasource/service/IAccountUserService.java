package com.guoranbot.datasource.service;


import com.guoranbot.common.dto.EkbUserInfo;
import com.guoranbot.common.dto.MergeUserParam;
import com.guoranbot.common.po.AccountUser;
import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.common.po.EkbStaffBind;
import com.guoranbot.common.po.Feedback;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2019-12-13
 */
public interface IAccountUserService {

    AccountUser selectOneByOpenId(String openId);

    /**
     * 通过微信 openId查找用户
     *
     * @param openid openid
     * @return AccountUser
     */
    AccountUser selectOneByWxOpenId(String openid);

    AccountUser selectOneByAliPayUserId(String aliPayUserId);

    AccountUser selectOneByPhone(String phone);

    int updateByPrimaryKeySelective(AccountUser update);

    AccountUser insertSelective(AccountUser insert);

    AccountUser selectById(Long id);

    /**
     * 添加邮箱
     *
     * @param userId   用户ID
     * @param email    邮箱
     * @param password password
     * @param aliPayUserId
     */
    int addEmail(Long userId, String email, String password,String wxOpenId,String aliPayUserId,Integer platform);

    /**
     * 添加反馈信息
     *
     * @param id 用户ID
     * @param feedback
     * @return 更新成功1
     */
    int addFeedBack(Long id, Feedback feedback);

    /**
     * 删除用户
     * @param userId
     * @param openId
     * @param aliPayUserId
     * @return
     */
    int delete(Long userId,String openId,String aliPayUserId);

    /**
     * 自建邮箱查找用户
     * @param customizeEmail
     * @return
     */
    AccountUser selectOneByCusEmail(String customizeEmail);

    /**
     * 合并用户账户
     * @param mergeUserParam
     * @return
     */
    int mergeUser(MergeUserParam mergeUserParam);

    List<AccountUser> selectWeChatUserInfoByIds(Collection<Long> keySet);
    List<AccountUser> selectWeChatUserInfoByOpenIds(Collection<String> keySet);
    List<AccountUser> selectUserInfoByOpenIds(Collection<String> keySet);

    int getTotalUser(Long startTime,Long endTime);

    int getMobileAuthUser(Long startTime,Long endTime);

    int getCusEmailAuthUser(Long startTime,Long endTime);

    List<EkbStaffBind> authBatch(List<EkbUserInfo> ekbUserInfos, String callBackUrl);

    /**
     * 获取所有AIFAPIAO邮箱账户
     * @return AIFAPIAO邮箱账户列表
     */
    List<AccountUserEmail> getAifapiaoEmailAccounts();

}
