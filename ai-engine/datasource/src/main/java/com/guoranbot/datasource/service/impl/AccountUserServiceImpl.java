package com.guoranbot.datasource.service.impl;


import com.guoranbot.common.dto.EkbUserInfo;
import com.guoranbot.common.dto.MergeUserParam;
import com.guoranbot.common.enums.EnumMergeUserScenes;
import com.guoranbot.common.po.*;
import com.guoranbot.common.utils.PasswordGenerator;
import com.guoranbot.datasource.dao.AccountUserMapper;
import com.guoranbot.datasource.dao.EkbStaffBindMapper;
import com.guoranbot.datasource.service.*;
import com.guoranbot.datasource.service.feign.IMailBoxFeignService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019-12-13
 */
@Slf4j
@Service
@AllArgsConstructor
public class AccountUserServiceImpl implements IAccountUserService {

    private final AccountUserMapper accountUserMapper;
    private final IAccountInitService accountInitService;
    private final IMailBoxFeignService mailBoxFeignService;
    private final IInvoiceInfoService invoiceInfoService;
    private final IInvoiceTypeService invoiceTypeService;
    private final IEkbStaffBindService ekbStaffBindService;
    private final EkbStaffBindMapper ekbStaffBindMapper;

    @Override
    public AccountUser selectOneByOpenId(String openId) {
        AccountUser accountUser = accountUserMapper.selectOne(AccountUser.builder().openId(openId).build());
        if(accountUser != null){
            accountInitService.increasingInvoiceType(accountUser.getOpenId());
        }
        return accountUser;
    }

//    @Cacheable(key = "#openid", cacheNames = "user:wx",unless = "#result == null")
    @Override
    public AccountUser selectOneByWxOpenId(String openid) {
        AccountUser accountUser = accountUserMapper.selectOne(AccountUser.builder().wxOpenId(openid).build());
        if (accountUser != null) {
            accountInitService.increasingInvoiceType(accountUser.getOpenId());
        }
        return accountUser;
    }

    @Cacheable(key = "#aliPayUserId", cacheNames = "user:alipay",unless = "#result == null")
    @Override
    public AccountUser selectOneByAliPayUserId(String aliPayUserId) {
        AccountUser accountUser = accountUserMapper.selectOne(AccountUser.builder().alipayUserId(aliPayUserId).build());
        if(accountUser != null){
            accountInitService.increasingInvoiceType(accountUser.getOpenId());
        }
        return accountUser;
    }

    @Override
    public AccountUser selectOneByPhone(String phone) {
        AccountUser accountUser = accountUserMapper.selectOne(AccountUser.builder().phone(phone).build());
        if(accountUser != null){
            accountInitService.increasingInvoiceType(accountUser.getOpenId());
        }
        return accountUser;
    }

    private static final PasswordGenerator PASSWORD_GENERATOR = new PasswordGenerator(10,3);

    @Caching(evict = {
            @CacheEvict(key = "#update.wxOpenId", cacheNames = "user:wx", condition = "#update.wxOpenId!=null"),
            @CacheEvict(key = "#update.id", cacheNames = "user:id", condition = "#update.id!=null"),
            @CacheEvict(key = "#update.alipayUserId", cacheNames = "user:alipay", condition = "#update.alipayUserId!=null"),
    })
    @Override
    public int updateByPrimaryKeySelective(AccountUser update) {

        int i = accountUserMapper.updateByPrimaryKeySelective(update);
        AccountUser accountUser = accountUserMapper.selectByPrimaryKey(update.getId());
        if (accountUser != null && accountUser.getPhone() != null && accountUser.getCustomizeEmail() == null) {
            try {
                String emailPassword = PASSWORD_GENERATOR.generateRandomPassword();
                String register = mailBoxFeignService.register(accountUser.getPhone(), emailPassword);
                AccountUserExtInfo extInfo = accountUser.getExtInfo();
                if (extInfo == null) {
                    extInfo = new AccountUserExtInfo();
                }
                extInfo.setCustomizeEmail(register);
                extInfo.setCustomizeEmailPassword(new String(Base64.getEncoder().encode(emailPassword.getBytes())));
                accountUser.setExtInfo(extInfo);
                log.info("注册自建邮箱 账户:{},密码:{}", register, emailPassword);
                accountUserMapper.updateByPrimaryKeySelective(accountUser);
            } catch (Exception e) {
                log.error("注册自建邮箱异常, phone:{}", accountUser.getPhone(), e);
            }
        }
        return i;
    }

    @Caching(put = {
            @CachePut(key = "#insert.wxOpenId", cacheNames = "user:wx", condition = "#insert.wxOpenId!=null"),
            @CachePut(key = "#insert.id", cacheNames = "user:id", condition = "#insert.id!=null"),
            @CachePut(key = "#insert.alipayUserId", cacheNames = "user:alipay", condition = "#insert.alipayUserId!=null")
    })
    @Override
    public AccountUser insertSelective(AccountUser insert) {
        int i = accountUserMapper.insertSelective(insert);
        if (i == 1) {
            accountInitService.initInvoiceType(insert.getId());
            return accountUserMapper.selectByPrimaryKey(insert.getId());
        }
        return null;
    }

    @Cacheable(key = "#id", cacheNames = "user:id",unless = "#result == null ")
    @Override
    public AccountUser selectById(Long id) {
        AccountUser accountUser =  accountUserMapper.selectOne(AccountUser.builder().id(id).build());
        if(accountUser != null){
            accountInitService.increasingInvoiceType(accountUser.getOpenId());
        }
        return accountUser;
    }


    @Override
    @Caching(evict = {
            @CacheEvict(cacheNames = "user:id", key = "#userId", condition = "#userId!=null"),
            @CacheEvict(cacheNames = "user:wx", key = "#wxOpenId", condition = "#wxOpenId!=null"),
            @CacheEvict(cacheNames = "user:alipay", key = "#aliPayUserId", condition = "#aliPayUserId!=null"),
    })
    public int addEmail(Long userId, String email, String password,String wxOpenId,String aliPayUserId,Integer platform) {
        AccountUser accountUser = selectById(userId);
        if (accountUser == null) {
            return 0;
        }
        AccountUserExtInfo extInfo = accountUser.getExtInfo();
        if (extInfo == null) {
            extInfo = new AccountUserExtInfo();
            accountUser.setExtInfo(extInfo);
        }
        List<AccountUserEmail> emails = extInfo.getEmails();
        if (emails == null) {
            emails = new ArrayList<>(1);
        }

        emails = emails.stream().filter(e -> !e.getEmail().equals(email)).collect(Collectors.toList());
        extInfo.setEmails(emails);

        List<AccountUserEmail> newResult = new ArrayList<>(emails.size()+1);
        AccountUserEmail accountUserEmail = new AccountUserEmail(email, password, platform);
        accountUserEmail.encodePassword();

        newResult.add(accountUserEmail);
        newResult.addAll(emails);
        extInfo.setEmails(newResult);

        return accountUserMapper.updateByPrimaryKeySelective(accountUser);
    }

    @Override
    public int addFeedBack(Long userId, Feedback feedback) {
        AccountUser accountUser = selectById(userId);
        if (accountUser == null) {
            return 0;
        }
        AccountUserExtInfo extInfo = accountUser.getExtInfo();
        if (extInfo == null) {
            extInfo = new AccountUserExtInfo();
            accountUser.setExtInfo(extInfo);
        }
        List<Feedback> feedbackList = extInfo.getFeedbackList();
        if (feedbackList == null) {
            feedbackList = new ArrayList<>(1);
            extInfo.setFeedbackList(feedbackList);
        }
        feedbackList.add(feedback);
        return accountUserMapper.updateByPrimaryKeySelective(AccountUser.builder().id(userId).extInfo(extInfo).build());
    }

    @Override
    @Caching(evict = {
            @CacheEvict(key = "#openId", cacheNames = "user:wx", condition = "#openId!=null"),
            @CacheEvict(key = "#userId", cacheNames = "user:id", condition = "#userId!=null"),
            @CacheEvict(key = "#aliPayUserId", cacheNames = "user:alipay", condition = "#aliPayUserId!=null")
    })
    public int delete(Long userId,String openId,String aliPayUserId) {
        return accountUserMapper.deleteByPrimaryKey(userId);
    }

    @Cacheable(key = "#customizeEmail", cacheNames = "user:cus-email",unless = "#result == null")
    @Override
    public AccountUser selectOneByCusEmail(String customizeEmail) {
        if (customizeEmail!=null){
            AccountUser accountUser = accountUserMapper.selectOne(AccountUser.builder().customizeEmail(customizeEmail).build());
            if (accountUser != null) {
                accountInitService.increasingInvoiceType(accountUser.getOpenId());
            }
            return accountUser;
        }
        return null;
    }

    @Caching(evict = {
            @CacheEvict(key = "#mergeUserParam.updateUser.wxOpenId", cacheNames = "user:wx", condition = "#mergeUserParam.updateUser.wxOpenId!=null"),
            @CacheEvict(key = "#mergeUserParam.deleteUser.wxOpenId", cacheNames = "user:wx", condition = "#mergeUserParam.deleteUser.wxOpenId!=null"),
            @CacheEvict(key = "#mergeUserParam.updateUser.alipayUserId", cacheNames = "user:alipay", condition = "#mergeUserParam.updateUser.alipayUserId!=null"),
            @CacheEvict(key = "#mergeUserParam.deleteUser.alipayUserId", cacheNames = "user:alipay", condition = "#mergeUserParam.deleteUser.alipayUserId!=null"),
            @CacheEvict(key = "#mergeUserParam.updateUser.id", cacheNames = "user:id", condition = "#mergeUserParam.updateUser.id!=null"),
            @CacheEvict(key = "#mergeUserParam.deleteUser.id", cacheNames = "user:id", condition = "#mergeUserParam.deleteUser.id!=null"),
    })
    @Transactional
    @Override
    public int mergeUser(MergeUserParam mergeUserParam) {
        AccountUser deleteUser = mergeUserParam.getDeleteUser();
        AccountUser updateUser = mergeUserParam.getUpdateUser();
        int delResult = delete(deleteUser.getId(), deleteUser.getWxOpenId(),deleteUser.getAlipayUserId());
        log.info("the phone is exist,del current user userId:{},openId:{},aliPayUserId:{}",deleteUser.getId(),
                deleteUser.getWxOpenId(),deleteUser.getAlipayUserId());
        if(delResult>0){
            int updateCount = updateByPrimaryKeySelective(updateUser);
            log.info("update the phone user info userId:{},result:{}",updateUser.getId(),updateCount);
            if(updateCount>0){
                //合并账号发票信息
                if(EnumMergeUserScenes.WX==mergeUserParam.getScenes()){
                    if(updateUser.getAlipayUserId()==null){
                        //已存在的用户是滴滴授权用户
                        int mergeCount=invoiceInfoService.mergeDiDiAndOthers(updateUser.getId(),deleteUser.getOpenId());
                        log.info("merge didi and wx user. userId:{},openId:{},result:{}",updateUser.getId(),deleteUser.getOpenId(),mergeCount);
                    }else{
                        //已存在用户是支付宝用户
                        int mergeCount=invoiceInfoService.mergeWxAndAliPay(deleteUser.getOpenId(),updateUser.getOpenId());
                        log.info("merge wx and aliPay user. deleteOpenId:{},updateOpenId:{},result:{}",deleteUser.getOpenId(),updateUser.getOpenId(),mergeCount);
                    }
                }else if(EnumMergeUserScenes.ALI_PAY==mergeUserParam.getScenes()){
                    if(updateUser.getWxOpenId()==null){
                        //已存在的用户是滴滴授权用户
                        int mergeCount=invoiceInfoService.mergeDiDiAndOthers(updateUser.getId(),deleteUser.getOpenId());
                        log.info("merge didi and aliPay user. userId:{},openId:{},result:{}",updateUser.getId(),deleteUser.getOpenId(),mergeCount);
                    }else{
                        //已存在用户是微信用户
                        int mergeCount=invoiceInfoService.mergeWxAndAliPay(deleteUser.getOpenId(),updateUser.getOpenId());
                        log.info("merge wx and aliPay user. deleteOpenId:{},updateOpenId:{},result:{}",deleteUser.getOpenId(),updateUser.getOpenId(),mergeCount);
                    }
                }
                //合并账号发票类型信息
                invoiceTypeService.merge(deleteUser.getOpenId(),updateUser.getOpenId());
            }
            return updateCount;
        }
        return 0;
    }

    @Override
    public List<AccountUser> selectWeChatUserInfoByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        Example example = new Example(AccountUser.class);
        example.createCriteria().andIn("id",ids);
        example.selectProperties("id","wxUserInfo");
        return accountUserMapper.selectByExample(example);
    }

    @Override
    public List<AccountUser> selectWeChatUserInfoByOpenIds(Collection<String> openIds) {
        if (CollectionUtils.isEmpty(openIds)){
            return Collections.emptyList();
        }
        Example example = new Example(AccountUser.class);
        example.createCriteria().andIn("openId",openIds);
        example.selectProperties("id","wxUserInfo","openId");
        return accountUserMapper.selectByExample(example);
    }

    @Override
    public List<AccountUser> selectUserInfoByOpenIds(Collection<String> openIds) {
        if (CollectionUtils.isEmpty(openIds)){
            return Collections.emptyList();
        }
        Example example = new Example(AccountUser.class);
        example.createCriteria().andIn("openId",openIds);
        return accountUserMapper.selectByExample(example);
    }

    @Override
    public int getTotalUser(Long startTime, Long endTime) {
        Example example=new Example(AccountUser.class);
        Example.Criteria criteria = example.createCriteria();
        addTimeParam(criteria,startTime,endTime);
        return accountUserMapper.selectCountByExample(example);
    }

    @Override
    public int getMobileAuthUser(Long startTime, Long endTime) {
        Example example=new Example(AccountUser.class);
        Example.Criteria criteria = example.createCriteria().andIsNotNull("phone");
        addTimeParam(criteria,startTime,endTime);
        return accountUserMapper.selectCountByExample(example);
    }

    @Override
    public int getCusEmailAuthUser(Long startTime, Long endTime) {
        Example example=new Example(AccountUser.class);
        Example.Criteria criteria = example.createCriteria().andIsNotNull("customizeEmail");
        addTimeParam(criteria,startTime,endTime);
        return accountUserMapper.selectCountByExample(example);
    }

    @Override
    public List<EkbStaffBind> authBatch(List<EkbUserInfo> ekbUserInfos, String callBackUrl) {
        if(CollectionUtils.isEmpty(ekbUserInfos)){
            return new ArrayList<>();
        }
//        log.info("start authBatch,param is:{},callBackUrl is:{}",ekbUserInfos,callBackUrl);
        Date now = new Date();
        String companyId = ekbUserInfos.get(0).getCompanyId();
        List<String> mobileList = ekbUserInfos.stream().map(EkbUserInfo::getMobile).collect(Collectors.toList());
        log.info("mobileList is:{}",mobileList);
        //找出已经注册过的人
        Example exampleUser = new Example(AccountUser.class);
        exampleUser.createCriteria().andIn("phone", mobileList).andEqualTo("active",Boolean.TRUE);
        List<AccountUser> accountUsers = accountUserMapper.selectByExample(exampleUser);
        Map<String, AccountUser> existUsers = accountUsers.stream().collect(Collectors.toMap(AccountUser::getPhone, accountUser -> accountUser, (k1,k2)->k1));
        //找出同企业下已经授权过的人
        Example exampleBind = new Example(EkbStaffBind.class);
        exampleBind.createCriteria().andIn("phone", mobileList).andEqualTo("ekbCompanyId",companyId).andEqualTo("active",Boolean.TRUE);
        List<EkbStaffBind> ekbStaffBinds = ekbStaffBindMapper.selectByExample(exampleBind);
        List<String> existBindPhones = ekbStaffBinds.stream().map(EkbStaffBind::getPhone).collect(Collectors.toList());
        //构建需要新注册 && 新绑定的电话号
        List<AccountUser> newAddUserList = new ArrayList<>();
        List<EkbStaffBind> ebkBindList = new ArrayList<>();
        for (EkbUserInfo ekbUserInfo : ekbUserInfos) {
            String mobile = ekbUserInfo.getMobile();
            String openId;
            if(!existUsers.containsKey(mobile)){
                //新注册用户
                openId = getOpenId();
                AccountUser newUser = AccountUser.builder().openId(openId).phone(ekbUserInfo.getMobile()).active(Boolean.TRUE).knowInvoiceCollection(false)
                        .skip(false).createTime(now).updateTime(now).build();
                try {
                    String emailPassword = PASSWORD_GENERATOR.generateRandomPassword();
                    String register = mailBoxFeignService.register(mobile, emailPassword);
                    newUser.setExtInfo(AccountUserExtInfo.builder().customizeEmail(register).customizeEmailPassword(new String(Base64.getEncoder().encode(emailPassword.getBytes()))).build());
                    log.info("Ekb批量授权:注册自建邮箱 账户:{},密码:{}", register, emailPassword);
                } catch (Exception e) {
                    log.error("Ekb批量授权:注册自建邮箱异常, phone:{}", mobile, e);
                }
                newAddUserList.add(newUser);
            }else {
                //已存在的用户
                openId = existUsers.get(mobile).getOpenId();
            }
            if(!existBindPhones.contains(mobile)){
                //没绑定过,绑定过不再绑定
                EkbStaffBind newBind = EkbStaffBind.builder().openId(openId).phone(ekbUserInfo.getMobile()).ekbUserId(ekbUserInfo.getUserId())
                        .createTime(now).updateTime(now).ekbCallbackUrl(callBackUrl)
                        .ekbCompanyId(ekbUserInfo.getCompanyId()).active(true).build();
                ebkBindList.add(newBind);
            }
        }
        if(CollectionUtils.isNotEmpty(newAddUserList)){
            log.info("newAddUserList is:{}",newAddUserList);
            accountUserMapper.insertList(newAddUserList);
        }
        if(CollectionUtils.isNotEmpty(ebkBindList)){
            log.info("ebkBindList is:{}",ebkBindList);
            ekbStaffBindService.insertList(ebkBindList);
        }
        return ekbStaffBindMapper.selectByExample(exampleBind);
    }

    private void addTimeParam(Example.Criteria criteria,Long startTime, Long endTime){
        if(startTime!=null){
            criteria.andGreaterThan("createTime",new Date(startTime));
        }
        if(endTime!=null){
            criteria.andLessThan("createTime",new Date(endTime));
        }
    }

    private String getOpenId(){
        return UUID.randomUUID().toString().replace("-","");
    }

    @Override
    public List<AccountUserEmail> getAifapiaoEmailAccounts() {
        log.info("开始获取所有AIFAPIAO邮箱账户");
        List<AccountUserEmail> aifapiaoEmails = new ArrayList<>();

        try {
            // 查询所有活跃用户
            Example example = new Example(AccountUser.class);
            example.createCriteria().andEqualTo("active", true);
            List<AccountUser> activeUsers = accountUserMapper.selectByExample(example);

            if (activeUsers == null || activeUsers.isEmpty()) {
                log.info("没有找到活跃用户");
                return aifapiaoEmails;
            }

            log.info("找到{}个活跃用户，开始筛选AIFAPIAO邮箱", activeUsers.size());

            for (AccountUser user : activeUsers) {
                AccountUserExtInfo extInfo = user.getExtInfo();
                if (extInfo == null) {
                    continue;
                }

                // 检查用户邮箱列表中的AIFAPIAO邮箱
                List<AccountUserEmail> emails = extInfo.getEmails();
                if (emails != null && !emails.isEmpty()) {
                    for (AccountUserEmail email : emails) {
                        if (isAifapiaoEmail(email.getEmail())) {
                            aifapiaoEmails.add(email);
                        }
                    }
                }

                // 检查自建邮箱是否是AIFAPIAO邮箱
                String customizeEmail = extInfo.getCustomizeEmail();
                String customizeEmailPassword = extInfo.getCustomizeEmailPassword();
                if (isAifapiaoEmail(customizeEmail) && customizeEmailPassword != null) {
                    AccountUserEmail customEmail = new AccountUserEmail(customizeEmail, customizeEmailPassword);
                    aifapiaoEmails.add(customEmail);
                }
            }

            log.info("成功获取到{}个AIFAPIAO邮箱账户", aifapiaoEmails.size());
            return aifapiaoEmails;

        } catch (Exception e) {
            log.error("获取AIFAPIAO邮箱账户时发生异常", e);
            return aifapiaoEmails;
        }
    }

    /**
     * 检查是否是AIFAPIAO邮箱
     */
    private boolean isAifapiaoEmail(String email) {
        return email != null && email.toLowerCase().endsWith("@aifapiao.com");
    }
}
