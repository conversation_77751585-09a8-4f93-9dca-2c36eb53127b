package com.guoranbot.datasource.controller;

import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.datasource.service.IAccountUserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmailCleanupControllerTest {

    @Mock
    private IAccountUserService accountUserService;

    @InjectMocks
    private EmailCleanupController emailCleanupController;

    @Test
    void testGetAifapiaoEmailAccounts() {
        // Given
        AccountUserEmail email1 = new AccountUserEmail("<EMAIL>", "password1");
        AccountUserEmail email2 = new AccountUserEmail("<EMAIL>", "password2");
        List<AccountUserEmail> mockAccounts = Arrays.asList(email1, email2);
        
        when(accountUserService.getAifapiaoEmailAccounts()).thenReturn(mockAccounts);

        // When
        ResponseEntity<List<AccountUserEmail>> response = emailCleanupController.getAifapiaoEmailAccounts();

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals("<EMAIL>", response.getBody().get(0).getEmail());
        assertEquals("<EMAIL>", response.getBody().get(1).getEmail());
    }

    @Test
    void testGetAifapiaoEmailAccounts_EmptyList() {
        // Given
        when(accountUserService.getAifapiaoEmailAccounts()).thenReturn(Arrays.asList());

        // When
        ResponseEntity<List<AccountUserEmail>> response = emailCleanupController.getAifapiaoEmailAccounts();

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
    }
}
