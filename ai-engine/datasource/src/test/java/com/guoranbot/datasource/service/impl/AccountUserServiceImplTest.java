package com.guoranbot.datasource.service.impl;

import com.guoranbot.common.po.AccountUser;
import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.common.po.AccountUserExtInfo;
import com.guoranbot.datasource.service.IAccountUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Profile("dev")
@Transactional
@Slf4j
public class AccountUserServiceImplTest {

    @Autowired
    IAccountUserService accountUserService;

    @Test
    void addEmail() {
        int i = accountUserService.addEmail(20085L, "<EMAIL>", "123", "oPSe25B7NvO6IOZevIiT1u7a8iok",null,0);
        assertEquals(1, i);
        AccountUser accountUser = accountUserService.selectById(20085L);
        List<AccountUserEmail> emails = accountUser.getExtInfo().getEmails();
        log.info("emails", emails);
        AccountUserEmail email = emails.get(0);
        assertEquals(email.getEmail(), "<EMAIL>");

        i = accountUserService.addEmail(20085L, "<EMAIL>", "123", "oPSe25B7NvO6IOZevIiT1u7a8iok",null,0);
        assertEquals(1, i);
        accountUser = accountUserService.selectById(20085L);
        emails = accountUser.getExtInfo().getEmails();
        log.info("emails", emails);
        email = emails.get(0);
        assertEquals(email.getEmail(), "<EMAIL>");
    }

    @Test
    @RepeatedTest(2)
    void cacheTest() {
        AccountUser accountUser = accountUserService.selectById(26L);
        AccountUserExtInfo extInfo = accountUser.getExtInfo();
        Boolean didiAuthStatus = extInfo.getDidiAuthStatus();
        assertEquals(Boolean.FALSE, didiAuthStatus);
    }

    @Test
    void testGetAifapiaoEmailAccounts() {
        // 测试获取AIFAPIAO邮箱账户
        List<AccountUserEmail> aifapiaoAccounts = accountUserService.getAifapiaoEmailAccounts();
        assertNotNull(aifapiaoAccounts);
        log.info("找到{}个AIFAPIAO邮箱账户", aifapiaoAccounts.size());

        // 验证所有返回的邮箱都是@aifapiao.com域名
        for (AccountUserEmail account : aifapiaoAccounts) {
            assertTrue(account.getEmail().endsWith("@aifapiao.com"),
                      "邮箱 " + account.getEmail() + " 不是AIFAPIAO域名");
            log.info("AIFAPIAO邮箱: {}", account.getEmail());
        }
    }
}
