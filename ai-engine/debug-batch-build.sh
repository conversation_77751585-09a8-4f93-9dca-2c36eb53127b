#!/bin/bash

# 调试批量构建脚本，查看是否包含 mini-program

set -e

echo "=== 调试批量构建逻辑 ==="

# 模拟 build-with-jib.sh 的批量构建逻辑
BUILD_ALL=true
PUSH_MODE=false

if [[ "$BUILD_ALL" == "true" ]]; then
    echo "📊 批量构建所有服务..."
    
    # 分阶段并行构建
    phase1=("common" "oss" "image")
    phase2=("eureka" "datasource" "email-parser" "pdf-parser" "enterprise-info")
    phase3=("analysis" "classify" "email" "message" "scheduler")
    phase4=("sms" "card-import" "dasource-listen" "customize-email-datasource")
    phase5=("toc-web" "client/didi-client" "client/ekb-client" "client/mini-program")
    
    all_phases=("phase1" "phase2" "phase3" "phase4" "phase5")
    
    for phase in "${all_phases[@]}"; do
        eval "services=(\${$phase[@]})"
        echo "🔄 构建阶段 $phase: ${services[*]}"
        
        for service in "${services[@]}"; do
            if [[ -d "$service" ]]; then
                echo "  ✓ 将构建服务: $service"
                if [[ "$service" == "client/mini-program" ]]; then
                    echo "    🎯 找到 mini-program 服务，将被构建！"
                fi
            else
                echo "  ❌ 跳过服务（目录不存在）: $service"
                if [[ "$service" == "client/mini-program" ]]; then
                    echo "    ⚠️  mini-program 被跳过！"
                fi
            fi
        done
        echo ""
    done
else
    echo "单个服务构建模式"
fi

echo "=== 调试完成 ==="
