# AI发票系统部署脚本说明

## 📋 脚本清单

### 🚀 主要部署脚本

| 脚本名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `script-manager.sh` | **脚本管理器** - 统一入口 | 推荐使用，提供菜单式操作 |
| `deploy-aifapiao.sh` | 部署AI发票系统 | 首次部署或重新部署 |
| `apply-health-patch.sh` | 应用健康检查补丁 | 解决服务启动超时问题 |
| `cleanup-failed-pods.sh` | 清理失败的Pod | 清理异常Pod释放资源 |

### 🔧 辅助工具脚本

| 脚本名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `validate-scripts.sh` | 脚本验证工具 | 检查脚本语法和完整性 |
| `test-health-patch.sh` | 测试健康检查配置 | 验证健康检查补丁效果 |
| `extract-app-resources.sh` | 提取应用资源 | 从统一配置中分离用户资源 |

### 📄 配置文件

| 文件名称 | 功能描述 | 权限要求 |
|---------|---------|---------|
| `aifapiao-admin-resources.yaml` | 管理员资源配置 | 需要管理员权限 |
| `aifapiao-app-resources.yaml` | 应用资源配置 | 用户权限即可 |
| `aifapiao-unified-deployment.yaml` | 统一部署配置 | 包含所有资源 |

## 🚀 快速开始

### 方法1：使用脚本管理器（推荐）

```bash
cd ai-engine/deploy
./script-manager.sh
```

然后根据菜单选择相应操作：
- 选择 `1` - 检查系统状态
- 选择 `2` - 部署AI发票系统
- 选择 `3` - 应用健康检查补丁

### 方法2：直接使用脚本

```bash
cd ai-engine/deploy

# 1. 首次部署
./deploy-aifapiao.sh

# 2. 应用健康检查补丁
./apply-health-patch.sh

# 3. 清理失败的Pod（如需要）
./cleanup-failed-pods.sh
```

## 🔧 常见问题解决

### 问题1：镜像拉取失败
**现象**：Pod状态为 `ImagePullBackOff` 或 `ErrImagePull`
**解决**：
1. 检查Docker仓库认证
2. 确保镜像标签正确
3. 运行清理脚本：`./cleanup-failed-pods.sh`

### 问题2：健康检查超时
**现象**：Pod状态为 `CrashLoopBackOff`，日志显示健康检查失败
**解决**：
```bash
./apply-health-patch.sh
```

### 问题3：资源配额超限
**现象**：Pod无法创建，提示资源配额超限
**解决**：
1. 扩大资源配额（需要管理员权限）
2. 清理不需要的Pod：`./cleanup-failed-pods.sh`

### 问题4：服务丢失
**现象**：某个服务的Pod不存在
**解决**：
1. 检查部署状态：`kubectl get deployment <服务名> -n aifapiao`
2. 重启部署：`kubectl rollout restart deployment <服务名> -n aifapiao`

## 📊 脚本验证

运行脚本验证工具检查所有脚本的完整性：

```bash
./validate-scripts.sh
```

## ⚠️ 注意事项

1. **不要轻易停止服务** - 所有服务都是重要的业务组件
2. **权限问题** - 某些操作需要管理员权限
3. **资源监控** - 定期检查资源使用情况
4. **备份重要** - 操作前建议备份重要配置

## 🔍 故障排查

### 检查Pod状态
```bash
kubectl get pods -n aifapiao
```

### 查看Pod详细信息
```bash
kubectl describe pod <pod名称> -n aifapiao
```

### 查看服务日志
```bash
kubectl logs <pod名称> -n aifapiao
```

### 检查资源配额
```bash
kubectl describe quota aifapiao-quota -n aifapiao
```

## 📞 支持

如果遇到问题，请：
1. 首先运行 `./script-manager.sh` 选择 `1` 检查系统状态
2. 查看相关日志和错误信息
3. 根据错误信息选择相应的解决方案

---

**版本**: 1.0.0  
**最后更新**: 2024年  
**维护者**: AI发票系统团队
