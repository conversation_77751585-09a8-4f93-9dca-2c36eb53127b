# AI发票系统统一Kubernetes部署指南

## 概述

本文档介绍如何使用 `aifapiao-unified-deployment.yaml` 统一部署文件来部署AI发票系统的所有微服务。该文件基于 `ai-engine/deploy` 目录下的现有配置整合而成，使用 **Nacos** 作为服务注册发现和配置中心。

## 文件说明

### 统一部署文件
- **文件名**: `aifapiao-unified-deployment.yaml`
- **位置**: `ai-engine/deploy/aifapiao-unified-deployment.yaml`
- **功能**: 包含所有微服务的完整Kubernetes部署配置

### 服务架构

#### 1. 数据服务层 (data-service) - 部署顺序: 1
- **data-service** (8764) - 数据源服务
- **email-parser-service** (8766) - 邮件解析服务
- **pdf-parser-service** (8759) - PDF解析服务

#### 2. 核心服务层 (core-service) - 部署顺序: 2
- **analysis-service** (8793) - 分析服务
- **classify-service** (8765) - 分类服务

#### 3. 应用服务层 (application-service) - 部署顺序: 3
- **email-service** (8760) - 邮件服务
- **message-service** (8762) - 消息服务
- **scheduler-service** (8767) - 调度服务
- **sms-service** (8770) - 短信服务
- **card-import-service** (8768) - 卡片导入服务

#### 4. 客户端服务层 (client-service) - 部署顺序: 4
- **mini-program-service** (8763) - 小程序服务
- **didi-client-service** (8763) - 滴滴客户端服务

## Nacos配置

### 服务器信息
- **地址**: `mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848`
- **命名空间**: `af7cd89a-e994-4667-ba69-d95ebac4788a`
- **分组**: `DEFAULT_GROUP`

### 环境变量配置
所有服务都配置了以下Nacos相关环境变量：
```yaml
- name: NACOS_ADDRESS
  valueFrom:
    configMapKeyRef:
      name: nacos-config
      key: nacos.server-addr
- name: NACOS_NAMESPACE
  valueFrom:
    configMapKeyRef:
      name: nacos-config
      key: nacos.namespace
- name: NACOS_GROUP
  valueFrom:
    configMapKeyRef:
      name: nacos-config
      key: nacos.group
```

## 部署方式

### 1. 一键部署（推荐）

```bash
# 进入部署目录
cd ai-engine/deploy

# 使用统一部署文件一键部署所有服务
kubectl apply -f aifapiao-unified-deployment.yaml

# 检查部署状态
kubectl get pods -n aifapiao -w
```

### 2. 分层部署

```bash
# 进入部署目录
cd ai-engine/deploy

# 1. 部署基础配置（命名空间、密钥、ConfigMap）
kubectl apply -f aifapiao-unified-deployment.yaml --selector="!app"

# 2. 部署数据服务层
kubectl apply -f aifapiao-unified-deployment.yaml --selector="component=data-service"

# 等待数据服务层启动完成
kubectl wait --for=condition=available --timeout=300s deployment -l component=data-service -n aifapiao

# 3. 部署核心服务层
kubectl apply -f aifapiao-unified-deployment.yaml --selector="component=core-service"

# 等待核心服务层启动完成
kubectl wait --for=condition=available --timeout=300s deployment -l component=core-service -n aifapiao

# 4. 部署应用服务层
kubectl apply -f aifapiao-unified-deployment.yaml --selector="component=application-service"

# 等待应用服务层启动完成
kubectl wait --for=condition=available --timeout=300s deployment -l component=application-service -n aifapiao

# 5. 部署客户端服务层
kubectl apply -f aifapiao-unified-deployment.yaml --selector="component=client-service"

# 等待客户端服务层启动完成
kubectl wait --for=condition=available --timeout=300s deployment -l component=client-service -n aifapiao
```

### 3. 使用现有脚本部署

```bash
# 使用现有的部署脚本（仍然有效）
cd ai-engine/deploy
./scripts/deploy-all.sh deploy
```

## 部署验证

### 1. 检查Pod状态
```bash
# 查看所有Pod状态
kubectl get pods -n aifapiao

# 查看特定组件的Pod
kubectl get pods -n aifapiao -l component=data-service
kubectl get pods -n aifapiao -l component=core-service
kubectl get pods -n aifapiao -l component=application-service
kubectl get pods -n aifapiao -l component=client-service
```

### 2. 检查服务状态
```bash
# 查看所有服务
kubectl get services -n aifapiao

# 查看服务详情
kubectl describe service data-service -n aifapiao
```

### 3. 健康检查
```bash
# 使用现有的健康检查脚本
cd ai-engine/deploy
./scripts/deploy-all.sh health

# 或手动检查特定服务
kubectl port-forward svc/data-service 8764:8764 -n aifapiao &
curl http://localhost:8764/actuator/health
```

### 4. 查看日志
```bash
# 查看特定服务日志
kubectl logs -f deployment/data-service -n aifapiao

# 查看所有服务日志
kubectl logs -f -l component=data-service -n aifapiao
```

## 资源配置

### 总体资源需求
- **CPU请求**: 约4.2 cores
- **CPU限制**: 约21 cores
- **内存请求**: 约8.5 GB
- **内存限制**: 约42 GB

### 各服务资源配置
| 服务 | CPU请求 | CPU限制 | 内存请求 | 内存限制 |
|------|---------|---------|----------|----------|
| data-service | 200m | 1000m | 512Mi | 2Gi |
| email-parser-service | 200m | 1000m | 512Mi | 2Gi |
| pdf-parser-service | 500m | 2000m | 1Gi | 4Gi |
| analysis-service | 500m | 2000m | 1Gi | 4Gi |
| classify-service | 200m | 1000m | 512Mi | 2Gi |
| email-service | 200m | 1000m | 512Mi | 2Gi |
| message-service | 100m | 500m | 256Mi | 1Gi |
| scheduler-service | 200m | 1000m | 512Mi | 2Gi |
| sms-service | 100m | 500m | 256Mi | 1Gi |
| card-import-service | 200m | 1000m | 512Mi | 2Gi |
| mini-program-service | 200m | 1000m | 512Mi | 2Gi |
| didi-client-service | 200m | 1000m | 512Mi | 2Gi |

## 监控和日志

### DataKit日志收集
所有服务都配置了DataKit日志收集注解：
```yaml
annotations:
  datakit/logs: |
    [
      {
        "disable": false,
        "source": "aifapiao-prod",
        "service": "service-name",
        "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
      }
    ]
```

### 健康检查端点
所有服务都提供以下端点：
- `/actuator/health` - 健康状态检查
- `/actuator/info` - 服务信息
- `/actuator/metrics` - 指标数据

## 故障排查

### 常见问题

1. **服务无法启动**
```bash
# 查看Pod事件
kubectl describe pod <pod-name> -n aifapiao

# 查看容器日志
kubectl logs <pod-name> -n aifapiao
```

2. **服务无法注册到Nacos**
```bash
# 检查Nacos配置
kubectl get configmap nacos-config -n aifapiao -o yaml

# 检查网络连通性
kubectl exec -it <pod-name> -n aifapiao -- nslookup mse-30e0a982-p.nacos-ans.mse.aliyuncs.com
```

3. **资源不足**
```bash
# 检查节点资源
kubectl top nodes

# 检查Pod资源使用
kubectl top pods -n aifapiao
```

### 回滚操作
```bash
# 回滚特定服务
kubectl rollout undo deployment/data-service -n aifapiao

# 查看回滚状态
kubectl rollout status deployment/data-service -n aifapiao
```

## 清理部署

```bash
# 删除所有服务
kubectl delete -f aifapiao-unified-deployment.yaml

# 或删除整个命名空间
kubectl delete namespace aifapiao
```

## 与现有部署方式的对比

| 特性 | 统一部署文件 | 现有分散部署 |
|------|-------------|-------------|
| 部署复杂度 | 简单，一个文件 | 复杂，多个目录 |
| 维护成本 | 低 | 高 |
| 配置一致性 | 高 | 中等 |
| 部署速度 | 快 | 中等 |
| 故障排查 | 容易 | 复杂 |
| 版本管理 | 统一 | 分散 |

## 注意事项

1. **部署前确认**：
   - Kubernetes集群资源充足
   - Nacos服务器正常运行
   - 镜像仓库访问权限正确

2. **生产环境建议**：
   - 使用分层部署方式
   - 监控资源使用情况
   - 定期备份配置文件

3. **配置管理**：
   - 确保Nacos配置已正确上传
   - 验证环境变量配置
   - 检查网络策略设置

---

**推荐使用统一部署文件进行部署，可以大大简化部署流程并提高维护效率。**
