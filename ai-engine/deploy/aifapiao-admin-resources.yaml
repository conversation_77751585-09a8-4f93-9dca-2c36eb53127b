# ========================================
# AI发票系统管理员资源配置文件
# 需要集群管理员权限才能创建的资源
# ========================================

# 命名空间
apiVersion: v1
kind: Namespace
metadata:
  name: aifapiao
  labels:
    name: aifapiao

---
# 资源配额
apiVersion: v1
kind: ResourceQuota
metadata:
  name: aifapiao-quota
  namespace: aifapiao
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "50"

---
# 限制范围
apiVersion: v1
kind: LimitRange
metadata:
  name: aifapiao-limits
  namespace: aifapiao
spec:
  limits:
  - type: Container
    default:
      cpu: 500m
      memory: 1Gi
    defaultRequest:
      cpu: 100m
      memory: 256Mi
    max:
      cpu: 2000m
      memory: 4Gi
    min:
      cpu: 50m
      memory: 128Mi

---
# Docker仓库密钥
apiVersion: v1
kind: Secret
metadata:
  name: tengxun-repo-xbox
  namespace: aifapiao
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************************************************************************************************************

---
# MySQL密钥
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secret
  namespace: aifapiao
type: Opaque
data:
  mysql-password: eGJveA==
  mysql-root-password: eGJveA==
  mysql-user: eGJveA==

---
# Redis密钥
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: aifapiao
type: Opaque
data:
  redis-password: eGJveA==
