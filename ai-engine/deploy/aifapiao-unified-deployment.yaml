# ========================================
# AI发票系统统一Kubernetes部署配置文件
# 基于 ai-engine/deploy 目录下的现有配置整合
# 服务发现: Nacos (mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848)
# 命名空间: af7cd89a-e994-4667-ba69-d95ebac4788a
# 版本: v1.0
# 创建时间: 2025-01-24
# ========================================

# 命名空间和资源配额
apiVersion: v1
kind: Namespace
metadata:
  name: aifapiao
  labels:
    name: aifapiao

---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: aifapiao-quota
  namespace: aifapiao
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "50"

---
apiVersion: v1
kind: LimitRange
metadata:
  name: aifapiao-limits
  namespace: aifapiao
spec:
  limits:
  - type: Container
    default:
      cpu: 500m
      memory: 1Gi
    defaultRequest:
      cpu: 100m
      memory: 256Mi
    max:
      cpu: 2000m
      memory: 4Gi
    min:
      cpu: 50m
      memory: 128Mi

---
# Docker仓库密钥
apiVersion: v1
kind: Secret
metadata:
  name: tengxun-repo-xbox
  namespace: aifapiao
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************************************************************************************************************

---
# MySQL密钥
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secret
  namespace: aifapiao
type: Opaque
data:
  mysql-root-password: dmdLTmN0JlIzWWlM
  mysql-password: dmdLTmN0JlIzWWlM
  mysql-user: eGJveA==

---
# Redis密钥
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: aifapiao
type: Opaque
data:
  redis-password: Z3J6aEAxMjM=

---
# Nacos配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: nacos-config
  namespace: aifapiao
data:
  nacos.server-addr: "mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848"
  nacos.namespace: "af7cd89a-e994-4667-ba69-d95ebac4788a"
  nacos.group: "DEFAULT_GROUP"

---
# ========================================
# 数据服务层 (data-service)
# 部署顺序: 1
# ========================================

# 数据源服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-service
  namespace: aifapiao
  labels:
    app: data-service
    component: data-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: data-service
  template:
    metadata:
      labels:
        app: data-service
        component: data-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "data-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: data-service
        image: ekb-repo.tencentcloudcr.com/xbox/data-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8764
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "data-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME) 
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0 
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST) 
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false 
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE 
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8764
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8764
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: data-service
  namespace: aifapiao
  labels:
    app: data-service
spec:
  type: ClusterIP
  ports:
  - port: 8764
    targetPort: 8764
    protocol: TCP
    name: http
  selector:
    app: data-service

---
# 邮件解析服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-parser-service
  namespace: aifapiao
  labels:
    app: email-parser-service
    component: data-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: email-parser-service
  template:
    metadata:
      labels:
        app: email-parser-service
        component: data-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "email-parser-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: email-parser-service
        image: ekb-repo.tencentcloudcr.com/xbox/email-parser-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8766
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "email-parser-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8766
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8766
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: email-parser-service
  namespace: aifapiao
  labels:
    app: email-parser-service
spec:
  type: ClusterIP
  ports:
  - port: 8766
    targetPort: 8766
    protocol: TCP
    name: http
  selector:
    app: email-parser-service

---
# PDF解析服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pdf-parser-service
  namespace: aifapiao
  labels:
    app: pdf-parser-service
    component: data-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pdf-parser-service
  template:
    metadata:
      labels:
        app: pdf-parser-service
        component: data-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "pdf-parser-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: pdf-parser-service
        image: ekb-repo.tencentcloudcr.com/xbox/pdf-parser-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8759
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "pdf-parser-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms1024m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8759
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8759
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: pdf-parser-service
  namespace: aifapiao
  labels:
    app: pdf-parser-service
spec:
  type: ClusterIP
  ports:
  - port: 8759
    targetPort: 8759
    protocol: TCP
    name: http
  selector:
    app: pdf-parser-service

---
# ========================================
# 核心服务层 (core-service)
# 部署顺序: 2
# ========================================

# 分析服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-service
  namespace: aifapiao
  labels:
    app: analysis-service
    component: core-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: analysis-service
  template:
    metadata:
      labels:
        app: analysis-service
        component: core-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "analysis-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: analysis-service
        image: ekb-repo.tencentcloudcr.com/xbox/analysis-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8793
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "analysis-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms1024m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8793
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8793
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: analysis-service
  namespace: aifapiao
  labels:
    app: analysis-service
spec:
  type: ClusterIP
  ports:
  - port: 8793
    targetPort: 8793
    protocol: TCP
    name: http
  selector:
    app: analysis-service

---
# 分类服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: classify-service
  namespace: aifapiao
  labels:
    app: classify-service
    component: core-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: classify-service
  template:
    metadata:
      labels:
        app: classify-service
        component: core-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "classify-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: classify-service
        image: ekb-repo.tencentcloudcr.com/xbox/classify-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8765
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "classify-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8765
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8765
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: classify-service
  namespace: aifapiao
  labels:
    app: classify-service
spec:
  type: ClusterIP
  ports:
  - port: 8765
    targetPort: 8765
    protocol: TCP
    name: http
  selector:
    app: classify-service

---
# ========================================
# 应用服务层 (application-service)
# 部署顺序: 3
# ========================================

# 邮件服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-service
  namespace: aifapiao
  labels:
    app: email-service
    component: application-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: email-service
  template:
    metadata:
      labels:
        app: email-service
        component: application-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "email-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: email-service
        image: ekb-repo.tencentcloudcr.com/xbox/email-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8760
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "email-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        # ===== 邮件服务器配置 =====
        - name: EMAIL_AIFAPIAO_HOST
          value: "************"  # 生产环境内网IP
        - name: EMAIL_AIFAPIAO_POP3_HOST
          value: "************"
        - name: EMAIL_AIFAPIAO_IMAP_HOST
          value: "************"
        - name: EMAIL_AIFAPIAO_SMTP_HOST
          value: "************"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8760
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8760
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: email-service
  namespace: aifapiao
  labels:
    app: email-service
spec:
  type: ClusterIP
  ports:
  - port: 8760
    targetPort: 8760
    protocol: TCP
    name: http
  selector:
    app: email-service

---
# 消息服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: message-service
  namespace: aifapiao
  labels:
    app: message-service
    component: application-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: message-service
  template:
    metadata:
      labels:
        app: message-service
        component: application-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "message-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: message-service
        image: ekb-repo.tencentcloudcr.com/xbox/message-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8762
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "message-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8762
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8762
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: message-service
  namespace: aifapiao
  labels:
    app: message-service
spec:
  type: ClusterIP
  ports:
  - port: 8762
    targetPort: 8762
    protocol: TCP
    name: http
  selector:
    app: message-service

---
# 调度服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler-service
  namespace: aifapiao
  labels:
    app: scheduler-service
    component: application-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: scheduler-service
  template:
    metadata:
      labels:
        app: scheduler-service
        component: application-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "scheduler-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: scheduler-service
        image: ekb-repo.tencentcloudcr.com/xbox/scheduler-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8767
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "scheduler-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8767
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8767
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: scheduler-service
  namespace: aifapiao
  labels:
    app: scheduler-service
spec:
  type: ClusterIP
  ports:
  - port: 8767
    targetPort: 8767
    protocol: TCP
    name: http
  selector:
    app: scheduler-service

---
# 短信服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sms-service
  namespace: aifapiao
  labels:
    app: sms-service
    component: application-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sms-service
  template:
    metadata:
      labels:
        app: sms-service
        component: application-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "sms-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: sms-service
        image: ekb-repo.tencentcloudcr.com/xbox/sms-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8770
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "sms-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8770
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8770
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: sms-service
  namespace: aifapiao
  labels:
    app: sms-service
spec:
  type: ClusterIP
  ports:
  - port: 8770
    targetPort: 8770
    protocol: TCP
    name: http
  selector:
    app: sms-service

---
# 卡片导入服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: card-import-service
  namespace: aifapiao
  labels:
    app: card-import-service
    component: application-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: card-import-service
  template:
    metadata:
      labels:
        app: card-import-service
        component: application-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "card-import-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: card-import-service
        image: ekb-repo.tencentcloudcr.com/xbox/card-import-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8768
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "card-import-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8768
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8768
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: card-import-service
  namespace: aifapiao
  labels:
    app: card-import-service
spec:
  type: ClusterIP
  ports:
  - port: 8768
    targetPort: 8768
    protocol: TCP
    name: http
  selector:
    app: card-import-service

---
# ========================================
# 客户端服务层 (client-service)
# 部署顺序: 4
# ========================================

# 小程序服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mini-program-service
  namespace: aifapiao
  labels:
    app: mini-program-service
    component: client-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: mini-program-service
  template:
    metadata:
      labels:
        app: mini-program-service
        component: client-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "mini-program-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: mini-program-service
        image: ekb-repo.tencentcloudcr.com/xbox/mini-program-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8763
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "mini-program-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8763
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8763
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: mini-program-service
  namespace: aifapiao
  labels:
    app: mini-program-service
spec:
  type: ClusterIP
  ports:
  - port: 8763
    targetPort: 8763
    protocol: TCP
    name: http
  selector:
    app: mini-program-service

---
# 滴滴客户端服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: didi-client-service
  namespace: aifapiao
  labels:
    app: didi-client-service
    component: client-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: didi-client-service
  template:
    metadata:
      labels:
        app: didi-client-service
        component: client-service
      annotations:
        datakit/logs: |
          [
            {
              "disable": false,
              "source": "aifapiao-dev",
              "service": "didi-client-service",
              "multiline_match": "^\\d+-\\d+-\\d+ \\d+:\\d+:\\d+(,\\d+)?"
            }
          ]
    spec:
      containers:
      - name: didi-client-service
        image: ekb-repo.tencentcloudcr.com/xbox/didi-client-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8771
          protocol: TCP
        env:
        - name: spring.profiles.active
          value: "dev"
        - name: NACOS_ADDRESS
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.server-addr
        - name: NACOS_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.namespace
        - name: NACOS_GROUP
          valueFrom:
            configMapKeyRef:
              name: nacos-config
              key: nacos.group
        - name: DD_ENV
          value: "aifapiao-dev"
        - name: DD_SERVICE_NAME
          value: "didi-client-service"
        - name: DD_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: DD_RATE
          value: "1"
        - name: "JAVA_TOOL_OPTIONS"
          value: >-
            -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
            -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME)
            -Ddd.env=$(DD_ENV) -Ddd.version=1.0
            -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST)
            -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false
            -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE
            -Ddd.jdbc.sql.obfuscation=TRUE
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8771
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8771
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      imagePullSecrets:
      - name: tengxun-repo-xbox
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: didi-client-service
  namespace: aifapiao
  labels:
    app: didi-client-service
spec:
  type: ClusterIP
  ports:
  - port: 8771
    targetPort: 8771
    protocol: TCP
    name: http
  selector:
    app: didi-client-service
