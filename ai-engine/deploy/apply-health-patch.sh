#!/bin/bash

# 应用健康检查超时时间补丁脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 开始应用健康检查超时时间补丁...${NC}"
echo "========================================"

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl 未安装或未配置"
    exit 1
fi

# 检查命名空间是否存在
if ! kubectl get namespace aifapiao &> /dev/null; then
    echo "❌ 命名空间 aifapiao 不存在，请先创建"
    exit 1
fi

# 定义所有需要更新的服务
SERVICES=(
    "data-service"
    "email-parser-service"
    "pdf-parser-service"
    "analysis-service"
    "classify-service"
    "email-service"
    "message-service"
    "scheduler-service"
    "sms-service"
    "card-import-service"
    "mini-program-service"
    "didi-client-service"
)

echo "📋 即将更新的服务列表:"
for service in "${SERVICES[@]}"; do
    echo "  - $service"
done

echo ""
echo "⚠️  确认要继续吗？这将重启所有服务 (y/n)"
read -r response

if [[ ! $response =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

echo ""
echo "🔧 开始逐个更新服务..."

# 定义健康检查补丁 JSON
HEALTH_PATCH='{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "CONTAINER_NAME",
            "readinessProbe": {
              "httpGet": {
                "path": "/actuator/health",
                "port": PORT_NUMBER
              },
              "initialDelaySeconds": 180,
              "periodSeconds": 30,
              "timeoutSeconds": 10,
              "successThreshold": 1,
              "failureThreshold": 10
            },
            "livenessProbe": {
              "httpGet": {
                "path": "/actuator/health",
                "port": PORT_NUMBER
              },
              "initialDelaySeconds": 240,
              "periodSeconds": 30,
              "timeoutSeconds": 10,
              "successThreshold": 1,
              "failureThreshold": 10
            }
          }
        ]
      }
    }
  }
}'

# 服务端口映射
declare -A SERVICE_PORTS=(
    ["data-service"]="8764"
    ["email-parser-service"]="8766"
    ["pdf-parser-service"]="8759"
    ["analysis-service"]="8793"
    ["classify-service"]="8765"
    ["email-service"]="8760"
    ["message-service"]="8762"
    ["scheduler-service"]="8767"
    ["sms-service"]="8770"
    ["card-import-service"]="8768"
    ["mini-program-service"]="8763"
    ["didi-client-service"]="8771"
)

failed_services=()

# 逐个更新服务
for service in "${SERVICES[@]}"; do
    echo "🔄 更新服务: $service"

    # 检查服务是否存在
    if ! kubectl get deployment "$service" -n aifapiao &> /dev/null; then
        echo "⚠️  服务 $service 不存在，跳过"
        continue
    fi

    # 获取端口号
    port=${SERVICE_PORTS[$service]}
    if [[ -z "$port" ]]; then
        echo "❌ 未找到服务 $service 的端口配置，跳过"
        continue
    fi

    # 替换补丁中的占位符
    current_patch=$(echo "$HEALTH_PATCH" | sed "s/CONTAINER_NAME/$service/g" | sed "s/PORT_NUMBER/$port/g")

    # 应用补丁
    if kubectl patch deployment "$service" -n aifapiao --type='merge' -p "$current_patch"; then
        echo "✅ 服务 $service 更新成功"
    else
        echo "❌ 服务 $service 更新失败"
        failed_services+=("$service")
    fi

    echo ""
done

# 检查是否有失败的服务
if [[ ${#failed_services[@]} -gt 0 ]]; then
    echo "❌ 以下服务更新失败:"
    for service in "${failed_services[@]}"; do
        echo "  - $service"
    done
    echo ""
    echo "💡 您可以手动检查这些服务的状态："
    echo "   kubectl describe deployment <服务名> -n aifapiao"
    exit 1
else
    echo "✅ 所有服务更新成功！"
fi

echo ""
echo "⏳ 等待服务重新启动..."
sleep 10

echo ""
echo "📊 检查服务状态:"
kubectl get pods -n aifapiao

echo ""
echo "🔍 检查最近事件:"
kubectl get events -n aifapiao --sort-by='.lastTimestamp' | tail -5

echo ""
echo "✨ 操作完成！"
echo "📋 如需查看详细日志，请运行:"
echo "   kubectl logs -f deployment/<服务名> -n aifapiao"
echo "❓ 如需检查健康检查状态，请运行:"
echo "   kubectl describe pod <pod名> -n aifapiao | grep -A 10 'Liveness\|Readiness'"