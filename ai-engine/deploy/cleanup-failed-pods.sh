#!/bin/bash

# 清理失败的Pod脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧹 清理失败的Pod${NC}"
echo "========================================"

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl 未安装或未配置${NC}"
    exit 1
fi

# 检查命名空间是否存在
if ! kubectl get namespace aifapiao &> /dev/null; then
    echo -e "${RED}❌ 命名空间 aifapiao 不存在${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 检查失败的Pod...${NC}"

# 获取失败的Pod
failed_pods=$(kubectl get pods -n aifapiao | grep -E "(ImagePullBackOff|CrashLoopBackOff|Error|ErrImagePull)" | awk '{print $1}' || true)

if [[ -z "$failed_pods" ]]; then
    echo -e "${GREEN}✅ 没有发现失败的Pod${NC}"
    exit 0
fi

echo -e "${YELLOW}发现以下失败的Pod:${NC}"
echo "$failed_pods" | while read pod; do
    if [[ -n "$pod" ]]; then
        status=$(kubectl get pod "$pod" -n aifapiao -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
        echo -e "${RED}  - $pod ($status)${NC}"
    fi
done

pod_count=$(echo "$failed_pods" | wc -l)
echo ""
echo -e "${YELLOW}总共发现 $pod_count 个失败的Pod${NC}"

echo ""
echo -e "${YELLOW}⚠️  确认要删除这些失败的Pod吗？(y/n)${NC}"
read -r response

if [[ ! $response =~ ^[Yy]$ ]]; then
    echo -e "${RED}❌ 清理已取消${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}🗑️  开始删除失败的Pod...${NC}"

deleted_count=0
failed_deletions=()

# 将Pod列表转换为数组以避免子shell问题
IFS=$'\n' read -d '' -r -a pod_array <<< "$failed_pods" || true

for pod in "${pod_array[@]}"; do
    if [[ -n "$pod" ]]; then
        echo -e "${BLUE}🔄 删除Pod: $pod${NC}"

        if kubectl delete pod "$pod" -n aifapiao --force --grace-period=0; then
            echo -e "${GREEN}✅ 删除成功: $pod${NC}"
            ((deleted_count++))
        else
            echo -e "${RED}❌ 删除失败: $pod${NC}"
            failed_deletions+=("$pod")
        fi

        echo ""
    fi
done

echo -e "${BLUE}📊 清理结果:${NC}"
echo -e "${GREEN}✅ 成功删除: $deleted_count 个Pod${NC}"
echo -e "${RED}❌ 删除失败: ${#failed_deletions[@]} 个Pod${NC}"

if [[ ${#failed_deletions[@]} -gt 0 ]]; then
    echo -e "${RED}删除失败的Pod:${NC}"
    for pod in "${failed_deletions[@]}"; do
        echo -e "${RED}  - $pod${NC}"
    done
fi

echo ""
echo -e "${BLUE}⏳ 等待Pod清理完成...${NC}"
sleep 5

echo ""
echo -e "${BLUE}📊 检查清理后的状态:${NC}"
remaining_failed=$(kubectl get pods -n aifapiao | grep -E "(ImagePullBackOff|CrashLoopBackOff|Error|ErrImagePull)" | wc -l || echo "0")
echo -e "${GREEN}剩余失败Pod: $remaining_failed 个${NC}"

echo ""
echo -e "${BLUE}💾 检查资源使用情况:${NC}"
kubectl top pods -n aifapiao --no-headers 2>/dev/null | head -5 || echo "无法获取资源使用情况"

echo ""
echo -e "${GREEN}✨ 清理完成！${NC}"
