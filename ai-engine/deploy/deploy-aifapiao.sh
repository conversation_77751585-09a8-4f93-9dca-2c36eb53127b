#!/bin/bash

# AI发票系统部署脚本
# 处理权限问题，分步部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 AI发票系统部署脚本${NC}"
echo "========================================"

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl 未安装或未配置${NC}"
    exit 1
fi

# 检查必要文件
required_files=("aifapiao-admin-resources.yaml" "aifapiao-app-resources.yaml")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo -e "${RED}❌ 文件 $file 不存在${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✅ 所有必要文件存在${NC}"

# 检查当前用户权限
echo ""
echo -e "${BLUE}🔍 检查用户权限...${NC}"

# 检查是否可以创建命名空间
if kubectl auth can-i create namespaces 2>/dev/null; then
    echo -e "${GREEN}✅ 具有创建命名空间权限${NC}"
    ADMIN_PERMISSIONS=true
else
    echo -e "${YELLOW}⚠️  没有创建命名空间权限${NC}"
    ADMIN_PERMISSIONS=false
fi

# 检查命名空间是否存在
if kubectl get namespace aifapiao &> /dev/null; then
    echo -e "${GREEN}✅ 命名空间 aifapiao 已存在${NC}"
    NAMESPACE_EXISTS=true
else
    echo -e "${YELLOW}⚠️  命名空间 aifapiao 不存在${NC}"
    NAMESPACE_EXISTS=false
fi

echo ""
echo -e "${BLUE}📋 部署计划:${NC}"

if [[ "$ADMIN_PERMISSIONS" == "true" ]]; then
    echo -e "${GREEN}1. 部署管理员资源 (Namespace, ResourceQuota, LimitRange, Secrets)${NC}"
    echo -e "${GREEN}2. 部署应用资源 (ConfigMap, Deployments, Services)${NC}"
elif [[ "$NAMESPACE_EXISTS" == "true" ]]; then
    echo -e "${YELLOW}1. 跳过管理员资源 (权限不足)${NC}"
    echo -e "${GREEN}2. 部署应用资源 (ConfigMap, Deployments, Services)${NC}"
else
    echo -e "${RED}❌ 无法部署: 命名空间不存在且没有创建权限${NC}"
    echo ""
    echo -e "${BLUE}💡 解决方案:${NC}"
    echo "1. 请联系集群管理员先运行:"
    echo "   kubectl apply -f aifapiao-admin-resources.yaml"
    echo "2. 然后您可以运行:"
    echo "   kubectl apply -f aifapiao-app-resources.yaml"
    exit 1
fi

echo ""
echo -e "${YELLOW}⚠️  确认要继续部署吗？(y/n)${NC}"
read -r response

if [[ ! $response =~ ^[Yy]$ ]]; then
    echo -e "${RED}❌ 部署已取消${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}🔧 开始部署...${NC}"

# 部署管理员资源
if [[ "$ADMIN_PERMISSIONS" == "true" ]]; then
    echo -e "${BLUE}📦 部署管理员资源...${NC}"
    if kubectl apply -f aifapiao-admin-resources.yaml; then
        echo -e "${GREEN}✅ 管理员资源部署成功${NC}"
    else
        echo -e "${RED}❌ 管理员资源部署失败${NC}"
        exit 1
    fi
    echo ""
fi

# 部署应用资源
echo -e "${BLUE}📦 部署应用资源...${NC}"
if kubectl apply -f aifapiao-app-resources.yaml; then
    echo -e "${GREEN}✅ 应用资源部署成功${NC}"
else
    echo -e "${RED}❌ 应用资源部署失败${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 5

echo ""
echo -e "${BLUE}📊 检查部署状态:${NC}"
kubectl get pods -n aifapiao

echo ""
echo -e "${BLUE}🔍 检查服务状态:${NC}"
kubectl get services -n aifapiao

echo ""
echo -e "${GREEN}✨ 部署完成！${NC}"
echo ""
echo -e "${BLUE}📋 后续操作:${NC}"
echo "1. 检查Pod状态: kubectl get pods -n aifapiao"
echo "2. 查看服务日志: kubectl logs -f deployment/<服务名> -n aifapiao"
echo "3. 应用健康检查补丁: ./apply-health-patch.sh"
