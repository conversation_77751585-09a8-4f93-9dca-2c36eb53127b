#!/bin/bash

# 从统一部署文件中提取应用资源（排除需要管理员权限的资源）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 提取应用资源...${NC}"

input_file="aifapiao-unified-deployment.yaml"
output_file="aifapiao-app-resources.yaml"

if [[ ! -f "$input_file" ]]; then
    echo "❌ 输入文件 $input_file 不存在"
    exit 1
fi

# 创建输出文件头部
cat > "$output_file" << 'EOF'
# ========================================
# AI发票系统应用资源配置文件
# 用户权限可以部署的资源
# 不包含: Namespace, ResourceQuota, LimitRange, Secret
# ========================================

EOF

# 使用 awk 脚本来过滤资源
awk '
BEGIN {
    skip = 0
    in_document = 0
    buffer = ""
}

# 检测文档分隔符
/^---/ {
    if (in_document && !skip) {
        print buffer
        print "---"
    }
    buffer = ""
    skip = 0
    in_document = 1
    next
}

# 检测需要跳过的资源类型
/^kind: (Namespace|ResourceQuota|LimitRange|Secret)$/ {
    skip = 1
    next
}

# 如果不跳过，则添加到缓冲区
{
    if (!skip) {
        if (buffer != "") {
            buffer = buffer "\n" $0
        } else {
            buffer = $0
        }
    }
}

# 文件结束时处理最后一个文档
END {
    if (in_document && !skip && buffer != "") {
        print buffer
    }
}
' "$input_file" >> "$output_file"

echo "✅ 应用资源已提取到 $output_file"
echo ""
echo "📋 提取的资源类型:"
grep "^kind:" "$output_file" | sort | uniq -c | sort -nr

echo ""
echo "💡 使用说明:"
echo "1. 管理员先部署: kubectl apply -f aifapiao-admin-resources.yaml"
echo "2. 用户部署应用: kubectl apply -f aifapiao-app-resources.yaml"
