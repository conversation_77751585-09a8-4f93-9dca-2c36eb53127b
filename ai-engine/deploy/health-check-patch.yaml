# 健康检查超时时间补丁文件
# 适用于所有AI发票系统服务

# 数据源服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: data-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8764
          initialDelaySeconds: 180  # 从60秒增加到180秒
          periodSeconds: 30         # 从20秒增加到30秒
          timeoutSeconds: 10        # 从5秒增加到10秒
          successThreshold: 1
          failureThreshold: 10      # 从3次增加到10次
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8764
          initialDelaySeconds: 240  # 从90秒增加到240秒
          periodSeconds: 30         # 从10秒增加到30秒
          timeoutSeconds: 10        # 从5秒增加到10秒
          successThreshold: 1
          failureThreshold: 10      # 从3次增加到10次

---
# 邮件解析服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-parser-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: email-parser-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8766
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8766
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# PDF解析服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pdf-parser-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: pdf-parser-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8759
          initialDelaySeconds: 240  # PDF服务需要更长时间
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8759
          initialDelaySeconds: 300
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 分析服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: analysis-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8793
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8793
          initialDelaySeconds: 300
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 分类服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: classify-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: classify-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8765
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8765
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 邮件服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: email-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8760
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8760
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 消息服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: message-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: message-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8762
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8762
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 调度服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: scheduler-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8767
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8767
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 短信服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sms-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: sms-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8770
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8770
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 卡片导入服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: card-import-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: card-import-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8768
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8768
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 小程序服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mini-program-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: mini-program-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8763
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8763
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10

---
# 滴滴客户端服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: didi-client-service
  namespace: aifapiao
spec:
  template:
    spec:
      containers:
      - name: didi-client-service
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8771
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8771
          initialDelaySeconds: 240
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 10