#!/bin/bash

# 快速应用健康检查超时时间补丁的命令集合
echo "🚀 快速应用健康检查超时时间补丁"
echo "========================================"

# 进入部署目录
cd $(dirname "$0")

# 检查kubectl连接
kubectl cluster-info > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 无法连接到Kubernetes集群"
    exit 1
fi

# 快速批量应用补丁（不重启服务，使用kubectl patch）
echo "🔧 开始应用补丁..."

# 数据服务层
kubectl patch deployment data-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"data-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment email-parser-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"email-parser-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment pdf-parser-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"pdf-parser-service","readinessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":300,"periodSeconds":30,"failureThreshold":10}}]}}}}'

# 核心服务层
kubectl patch deployment analysis-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"analysis-service","readinessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":300,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment classify-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"classify-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

# 应用服务层
kubectl patch deployment email-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"email-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment message-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"message-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment scheduler-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"scheduler-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment sms-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"sms-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment card-import-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"card-import-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

# 客户端服务层
kubectl patch deployment mini-program-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"mini-program-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

kubectl patch deployment didi-client-service -n aifapiao -p '{"spec":{"template":{"spec":{"containers":[{"name":"didi-client-service","readinessProbe":{"initialDelaySeconds":180,"periodSeconds":30,"failureThreshold":10},"livenessProbe":{"initialDelaySeconds":240,"periodSeconds":30,"failureThreshold":10}}]}}}}'

echo "✅ 所有服务的健康检查超时时间已更新！"
echo ""
echo "📊 正在检查服务状态..."
sleep 5
kubectl get pods -n aifapiao

echo ""
echo "📝 更新后的健康检查参数:"
echo "  - InitialDelay: 180-300秒 (根据服务类型)"
echo "  - Period: 30秒"
echo "  - FailureThreshold: 10次"
echo "  - Timeout: 10秒"
echo ""
echo "⏰ 现在服务有更多时间启动，请耐心等待3-5分钟"