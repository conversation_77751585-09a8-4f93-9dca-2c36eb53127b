#!/bin/bash

# AI发票系统部署脚本管理器
# 整合所有部署相关脚本的统一入口

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本版本
VERSION="1.0.0"

echo -e "${BLUE}🚀 AI发票系统部署脚本管理器 v${VERSION}${NC}"
echo "========================================"

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl 未安装或未配置${NC}"
    exit 1
fi

# 检查命名空间是否存在
if ! kubectl get namespace aifapiao &> /dev/null; then
    echo -e "${YELLOW}⚠️  命名空间 aifapiao 不存在${NC}"
    NAMESPACE_EXISTS=false
else
    echo -e "${GREEN}✅ 命名空间 aifapiao 已存在${NC}"
    NAMESPACE_EXISTS=true
fi

# 脚本验证函数
validate_scripts() {
    echo -e "${BLUE}🔍 验证脚本完整性...${NC}"
    
    local scripts=(
        "apply-health-patch.sh"
        "deploy-aifapiao.sh"
        "cleanup-failed-pods.sh"
        "extract-app-resources.sh"
        "test-health-patch.sh"
    )
    
    local missing_scripts=()
    local valid_scripts=()
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if [[ -x "$script" ]]; then
                echo -e "${GREEN}  ✅ $script (可执行)${NC}"
                valid_scripts+=("$script")
            else
                echo -e "${YELLOW}  ⚠️  $script (不可执行，正在修复...)${NC}"
                chmod +x "$script"
                valid_scripts+=("$script")
            fi
        else
            echo -e "${RED}  ❌ $script (缺失)${NC}"
            missing_scripts+=("$script")
        fi
    done
    
    if [[ ${#missing_scripts[@]} -gt 0 ]]; then
        echo -e "${RED}缺失的脚本: ${missing_scripts[*]}${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 所有脚本验证通过${NC}"
    return 0
}

# 显示菜单
show_menu() {
    echo ""
    echo -e "${CYAN}📋 可用操作:${NC}"
    echo "1. 🏥 检查系统状态"
    echo "2. 🚀 部署AI发票系统"
    echo "3. 💊 应用健康检查补丁"
    echo "4. 🧹 清理失败的Pod"
    echo "5. 🔧 测试健康检查"
    echo "6. 📊 查看资源使用情况"
    echo "7. 🔍 验证脚本完整性"
    echo "8. 📖 显示帮助信息"
    echo "0. 🚪 退出"
    echo ""
    echo -e "${YELLOW}请选择操作 (0-8):${NC}"
}

# 检查系统状态
check_system_status() {
    echo -e "${BLUE}🏥 检查系统状态...${NC}"
    echo ""
    
    echo -e "${CYAN}📊 Pod状态统计:${NC}"
    kubectl get pods -n aifapiao --no-headers 2>/dev/null | awk '{print $3}' | sort | uniq -c | while read count status; do
        case $status in
            "Running")
                echo -e "${GREEN}  ✅ Running: $count${NC}"
                ;;
            "Pending")
                echo -e "${YELLOW}  ⏳ Pending: $count${NC}"
                ;;
            "CrashLoopBackOff"|"ImagePullBackOff"|"Error"|"ErrImagePull")
                echo -e "${RED}  ❌ $status: $count${NC}"
                ;;
            *)
                echo -e "${CYAN}  ℹ️  $status: $count${NC}"
                ;;
        esac
    done
    
    echo ""
    echo -e "${CYAN}💾 资源配额使用情况:${NC}"
    kubectl describe quota aifapiao-quota -n aifapiao 2>/dev/null | grep -A 10 "Resource" || echo "无法获取资源配额信息"
    
    echo ""
    echo -e "${CYAN}🔧 服务部署状态:${NC}"
    kubectl get deployments -n aifapiao --no-headers 2>/dev/null | while read name ready uptodate available age; do
        if [[ "$ready" == "0/0" ]]; then
            echo -e "${RED}  ❌ $name: $ready${NC}"
        elif [[ "$ready" =~ ^[0-9]+/[0-9]+$ ]] && [[ "${ready%/*}" == "${ready#*/}" ]]; then
            echo -e "${GREEN}  ✅ $name: $ready${NC}"
        else
            echo -e "${YELLOW}  ⚠️  $name: $ready${NC}"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}📖 AI发票系统部署脚本管理器帮助${NC}"
    echo ""
    echo -e "${CYAN}脚本说明:${NC}"
    echo "• apply-health-patch.sh    - 应用健康检查超时补丁"
    echo "• deploy-aifapiao.sh       - 部署AI发票系统"
    echo "• cleanup-failed-pods.sh   - 清理失败的Pod"
    echo "• test-health-patch.sh     - 测试健康检查配置"
    echo ""
    echo -e "${CYAN}使用建议:${NC}"
    echo "1. 首次部署: 选择选项2 (部署AI发票系统)"
    echo "2. 服务异常: 选择选项1 (检查系统状态) → 选项4 (清理失败Pod)"
    echo "3. 健康检查问题: 选择选项3 (应用健康检查补丁)"
    echo "4. 定期维护: 选择选项6 (查看资源使用情况)"
    echo ""
    echo -e "${CYAN}注意事项:${NC}"
    echo "• 所有操作都会要求确认，请仔细阅读提示"
    echo "• 不要轻易停止正在运行的服务"
    echo "• 建议在非业务高峰期进行维护操作"
}

# 主循环
main() {
    # 验证脚本
    if ! validate_scripts; then
        echo -e "${RED}❌ 脚本验证失败，请检查缺失的文件${NC}"
        exit 1
    fi
    
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                check_system_status
                ;;
            2)
                echo -e "${BLUE}🚀 启动部署脚本...${NC}"
                ./deploy-aifapiao.sh
                ;;
            3)
                echo -e "${BLUE}💊 启动健康检查补丁...${NC}"
                ./apply-health-patch.sh
                ;;
            4)
                echo -e "${BLUE}🧹 启动Pod清理...${NC}"
                ./cleanup-failed-pods.sh
                ;;
            5)
                echo -e "${BLUE}🔧 启动健康检查测试...${NC}"
                ./test-health-patch.sh
                ;;
            6)
                echo -e "${BLUE}📊 查看资源使用情况...${NC}"
                kubectl top pods -n aifapiao 2>/dev/null || echo "无法获取资源使用情况"
                echo ""
                kubectl describe quota aifapiao-quota -n aifapiao 2>/dev/null || echo "无法获取资源配额信息"
                ;;
            7)
                validate_scripts
                ;;
            8)
                show_help
                ;;
            0)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请输入 0-8${NC}"
                ;;
        esac
        
        echo ""
        echo -e "${CYAN}按回车键继续...${NC}"
        read -r
    done
}

# 启动主程序
main "$@"
