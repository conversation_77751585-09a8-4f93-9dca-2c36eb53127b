#!/bin/bash

# 测试健康检查补丁脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 测试健康检查补丁脚本...${NC}"
echo "========================================"

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl 未安装或未配置"
    exit 1
fi

# 检查命名空间是否存在
echo "🔍 检查命名空间..."
if ! kubectl get namespace aifapiao &> /dev/null; then
    echo "❌ 命名空间 aifapiao 不存在，请先创建"
    exit 1
fi

echo "✅ 命名空间 aifapiao 存在"

# 检查现有的服务
echo ""
echo "📋 检查现有服务..."
SERVICES=(
    "data-service"
    "email-parser-service"
    "pdf-parser-service"
    "analysis-service"
    "classify-service"
    "email-service"
    "message-service"
    "scheduler-service"
    "sms-service"
    "card-import-service"
    "mini-program-service"
    "didi-client-service"
)

existing_services=()
missing_services=()

for service in "${SERVICES[@]}"; do
    if kubectl get deployment "$service" -n aifapiao &> /dev/null; then
        echo "✅ 服务存在: $service"
        existing_services+=("$service")
    else
        echo "❌ 服务不存在: $service"
        missing_services+=("$service")
    fi
done

echo ""
echo "📊 统计结果:"
echo "  - 存在的服务: ${#existing_services[@]}"
echo "  - 缺失的服务: ${#missing_services[@]}"

if [[ ${#existing_services[@]} -eq 0 ]]; then
    echo "❌ 没有找到任何服务，无法应用补丁"
    exit 1
fi

echo ""
echo "🔧 测试单个服务补丁应用..."

# 选择第一个存在的服务进行测试
test_service="${existing_services[0]}"
echo "🎯 测试服务: $test_service"

# 定义健康检查补丁 JSON
HEALTH_PATCH='{
  "spec": {
    "template": {
      "spec": {
        "containers": [
          {
            "name": "'$test_service'",
            "readinessProbe": {
              "httpGet": {
                "path": "/actuator/health",
                "port": 8764
              },
              "initialDelaySeconds": 180,
              "periodSeconds": 30,
              "timeoutSeconds": 10,
              "successThreshold": 1,
              "failureThreshold": 10
            },
            "livenessProbe": {
              "httpGet": {
                "path": "/actuator/health",
                "port": 8764
              },
              "initialDelaySeconds": 240,
              "periodSeconds": 30,
              "timeoutSeconds": 10,
              "successThreshold": 1,
              "failureThreshold": 10
            }
          }
        ]
      }
    }
  }
}'

echo "📄 应用补丁到 $test_service..."
echo "补丁内容预览:"
echo "$HEALTH_PATCH" | head -10

echo ""
echo "🔄 执行 kubectl patch 命令..."

if kubectl patch deployment "$test_service" -n aifapiao --type='merge' -p "$HEALTH_PATCH" --dry-run=client; then
    echo "✅ 补丁语法验证成功（dry-run）"
    
    echo ""
    echo "⚠️  确认要应用真实补丁吗？(y/n)"
    read -r response
    
    if [[ $response =~ ^[Yy]$ ]]; then
        if kubectl patch deployment "$test_service" -n aifapiao --type='merge' -p "$HEALTH_PATCH"; then
            echo "✅ 补丁应用成功！"
        else
            echo "❌ 补丁应用失败"
            exit 1
        fi
    else
        echo "❌ 操作已取消"
    fi
else
    echo "❌ 补丁语法验证失败"
    exit 1
fi

echo ""
echo "✨ 测试完成！"
