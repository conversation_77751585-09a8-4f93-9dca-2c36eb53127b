#!/bin/bash

# 脚本验证工具
# 检查deploy目录下所有脚本的语法和逻辑错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 AI发票系统脚本验证工具${NC}"
echo "========================================"

# 要检查的脚本列表
SCRIPTS=(
    "apply-health-patch.sh"
    "deploy-aifapiao.sh"
    "cleanup-failed-pods.sh"
    "extract-app-resources.sh"
    "test-health-patch.sh"
    "script-manager.sh"
)

total_scripts=${#SCRIPTS[@]}
valid_scripts=0
invalid_scripts=0

echo -e "${BLUE}📋 检查 $total_scripts 个脚本...${NC}"
echo ""

for script in "${SCRIPTS[@]}"; do
    echo -e "${CYAN}🔍 检查脚本: $script${NC}"
    
    # 检查文件是否存在
    if [[ ! -f "$script" ]]; then
        echo -e "${RED}  ❌ 文件不存在${NC}"
        ((invalid_scripts++))
        continue
    fi
    
    # 检查文件权限
    if [[ ! -x "$script" ]]; then
        echo -e "${YELLOW}  ⚠️  文件不可执行，正在修复...${NC}"
        chmod +x "$script"
    fi
    
    # 语法检查
    if bash -n "$script" 2>/dev/null; then
        echo -e "${GREEN}  ✅ 语法检查通过${NC}"
    else
        echo -e "${RED}  ❌ 语法错误:${NC}"
        bash -n "$script" 2>&1 | sed 's/^/    /'
        ((invalid_scripts++))
        continue
    fi
    
    # 检查shebang
    first_line=$(head -n1 "$script")
    if [[ "$first_line" =~ ^#!/bin/bash ]]; then
        echo -e "${GREEN}  ✅ Shebang正确${NC}"
    else
        echo -e "${YELLOW}  ⚠️  Shebang可能有问题: $first_line${NC}"
    fi
    
    # 检查基本结构
    if grep -q "set -e" "$script"; then
        echo -e "${GREEN}  ✅ 包含错误处理${NC}"
    else
        echo -e "${YELLOW}  ⚠️  缺少 'set -e' 错误处理${NC}"
    fi
    
    # 检查颜色定义
    if grep -q "RED=.*033" "$script"; then
        echo -e "${GREEN}  ✅ 包含颜色定义${NC}"
    else
        echo -e "${YELLOW}  ⚠️  缺少颜色定义${NC}"
    fi
    
    # 特定脚本的检查
    case "$script" in
        "apply-health-patch.sh")
            if grep -q "SERVICE_PORTS" "$script"; then
                echo -e "${GREEN}  ✅ 包含服务端口配置${NC}"
            else
                echo -e "${RED}  ❌ 缺少服务端口配置${NC}"
            fi
            ;;
        "deploy-aifapiao.sh")
            if grep -q "aifapiao-admin-resources.yaml\|aifapiao-app-resources.yaml" "$script"; then
                echo -e "${GREEN}  ✅ 包含资源文件引用${NC}"
            else
                echo -e "${RED}  ❌ 缺少资源文件引用${NC}"
            fi
            ;;
        "cleanup-failed-pods.sh")
            if grep -q "ImagePullBackOff\|CrashLoopBackOff" "$script"; then
                echo -e "${GREEN}  ✅ 包含Pod状态检查${NC}"
            else
                echo -e "${RED}  ❌ 缺少Pod状态检查${NC}"
            fi
            ;;
    esac
    
    ((valid_scripts++))
    echo -e "${GREEN}  ✅ 脚本验证通过${NC}"
    echo ""
done

echo "========================================"
echo -e "${BLUE}📊 验证结果:${NC}"
echo -e "${GREEN}✅ 有效脚本: $valid_scripts${NC}"
echo -e "${RED}❌ 无效脚本: $invalid_scripts${NC}"

if [[ $invalid_scripts -eq 0 ]]; then
    echo -e "${GREEN}🎉 所有脚本验证通过！${NC}"
    exit 0
else
    echo -e "${RED}⚠️  发现 $invalid_scripts 个问题脚本，请检查并修复${NC}"
    exit 1
fi
