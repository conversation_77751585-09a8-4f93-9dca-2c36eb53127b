# Jib JAVA_OPTS 支持迁移指南

## 🎯 目标

将Jib构建的Docker镜像配置为支持运行时通过环境变量动态配置JVM参数，实现与Dockerfile `$JAVA_OPTS` 相同的灵活性。

## 🔍 问题分析

### 当前问题
- Jib默认将JVM参数硬编码到镜像中
- 无法在Kubernetes部署时动态调整内存、GC参数
- 不支持运行时环境变量覆盖

### 解决方案
使用`JAVA_TOOL_OPTIONS`环境变量，这是Java官方支持的运行时JVM参数配置方式。

## 🛠️ 迁移步骤

### 1. 修改Jib配置

**修改前（硬编码）：**
```xml
<jvmFlags>
    <jvmFlag>-server</jvmFlag>
    <jvmFlag>-Xms512m</jvmFlag>
    <jvmFlag>-Xmx1024m</jvmFlag>
    <jvmFlag>-XX:+UseG1GC</jvmFlag>
    <jvmFlag>-XX:MaxGCPauseMillis=100</jvmFlag>
    <jvmFlag>-XX:+UseStringDeduplication</jvmFlag>
    <jvmFlag>-Djava.security.egd=file:/dev/./urandom</jvmFlag>
    <jvmFlag>-Duser.timezone=Asia/Shanghai</jvmFlag>
    <jvmFlag>-javaagent:/dd-java-agent-v1.14.0-guance.jar</jvmFlag>
</jvmFlags>
```

**修改后（支持动态配置）：**
```xml
<!-- 基础JVM参数 - 保留必要的固定参数 -->
<jvmFlags>
    <jvmFlag>-server</jvmFlag>
    <jvmFlag>-Djava.security.egd=file:/dev/./urandom</jvmFlag>
    <jvmFlag>-Duser.timezone=Asia/Shanghai</jvmFlag>
    <jvmFlag>-javaagent:/dd-java-agent-v1.14.0-guance.jar</jvmFlag>
</jvmFlags>
<!-- 注意：内存和GC参数通过JAVA_TOOL_OPTIONS环境变量在运行时配置 -->
```

### 2. Kubernetes部署配置

```yaml
env:
- name: spring.profiles.active
  value: "prod"
- name: DD_ENV
  value: "aifapiao-prod"
- name: DD_SERVICE_NAME
  value: "service-name"
- name: DD_AGENT_HOST
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: status.hostIP
- name: POD_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.name
- name: DD_RATE
  value: "1"
# 使用JAVA_TOOL_OPTIONS配置JVM参数
- name: "JAVA_TOOL_OPTIONS"
  value: >-
    -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication
    -Ddd.logs.injection=true -Ddd.trace.sample.rate=$(DD_RATE) -Ddd.service=$(DD_SERVICE_NAME) 
    -Ddd.env=$(DD_ENV) -Ddd.version=1.0 
    -Ddd.tags=container_host:$(POD_NAME),pod_name:$(POD_NAME),node_ip:$(DD_AGENT_HOST) 
    -Ddd.agent.port=9529 -Ddd.jmxfetch.enabled=false 
    -Ddd.http.server.tag.query-string=TRUE -Ddd.trace.db.client.split-by-instance=TRUE 
    -Ddd.jdbc.sql.obfuscation=TRUE
```

## 📋 需要迁移的服务列表

### ✅ 已迁移
- [x] card-import

### 🔄 待迁移
- [ ] analysis
- [ ] classify  
- [ ] email
- [ ] email-parser
- [ ] pdf-parser
- [ ] message
- [ ] scheduler
- [ ] sms
- [ ] eureka
- [ ] datasource
- [ ] enterprise-info
- [ ] dasource-listen
- [ ] customize-email-datasource
- [ ] toc-web
- [ ] client/didi-client
- [ ] client/ekb-client
- [ ] client/mini-program

## 🧪 测试验证

### 1. 本地测试
```bash
# 构建镜像
./build-with-jib.sh card-import

# 测试JAVA_TOOL_OPTIONS
docker run -e JAVA_TOOL_OPTIONS="-Xms256m -Xmx512m" \
  ekb-repo.tencentcloudcr.com/xbox/card-import-service:latest
```

### 2. Kubernetes测试
```bash
# 部署服务
kubectl apply -f deploy/application/card-import-deployment.yaml

# 检查JVM参数
kubectl logs -n aifapiao deployment/card-import-service | grep "Picked up JAVA_TOOL_OPTIONS"
```

## 🔧 最佳实践

### 1. 参数分类
- **固定参数**：在Jib配置中设置（如timezone、security、javaagent）
- **动态参数**：通过JAVA_TOOL_OPTIONS设置（如内存、GC、监控参数）

### 2. 环境变量管理
- 使用ConfigMap管理通用JVM参数
- 使用环境特定的参数覆盖
- 支持Pod级别的参数定制

### 3. 监控集成
- 保持DataDog APM参数的灵活配置
- 支持动态调整采样率和标签

## ⚠️ 注意事项

1. **JAVA_TOOL_OPTIONS vs JAVA_OPTS**
   - Jib支持`JAVA_TOOL_OPTIONS`（Java官方标准）
   - 不支持`JAVA_OPTS`（非标准环境变量）

2. **参数优先级**
   - JAVA_TOOL_OPTIONS会追加到Jib配置的JVM参数之后
   - 相同参数以后者为准

3. **日志输出**
   - Java会在启动时输出"Picked up JAVA_TOOL_OPTIONS"信息
   - 这是正常行为，不是错误

## 🚀 迁移计划

1. **阶段1**：修改card-import服务配置（已完成）
2. **阶段2**：批量更新其他服务的Jib配置
3. **阶段3**：更新所有Kubernetes部署文件
4. **阶段4**：测试验证和文档更新
