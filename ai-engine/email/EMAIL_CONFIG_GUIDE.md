# 邮件服务器配置指南

## 概述

本指南说明如何配置 AIFAPIAO 邮件服务器地址，支持通过配置文件和环境变量灵活配置不同环境的邮件服务器。

## 配置方式

### 1. 环境变量配置（推荐）

通过环境变量配置，适用于容器化部署：

```bash
# 通用配置（所有服务使用相同地址）
export EMAIL_AIFAPIAO_HOST=************

# 或者分别配置各个服务
export EMAIL_AIFAPIAO_POP3_HOST=************
export EMAIL_AIFAPIAO_IMAP_HOST=************
export EMAIL_AIFAPIAO_SMTP_HOST=************
```

### 2. 配置文件配置

在 `application.yml` 或 `bootstrap.yml` 中配置：

```yaml
email:
  aifapiao:
    host: ************
    # 或者分别配置
    pop3:
      host: ************
    imap:
      host: ************
    smtp:
      host: ************
```

### 3. Nacos 配置中心

在 Nacos 的 `aifapiao-config.yaml` 中配置：

```yaml
email:
  aifapiao:
    host: ${EMAIL_AIFAPIAO_HOST:************}
    pop3:
      host: ${EMAIL_AIFAPIAO_POP3_HOST:${EMAIL_AIFAPIAO_HOST:************}}
    imap:
      host: ${EMAIL_AIFAPIAO_IMAP_HOST:${EMAIL_AIFAPIAO_HOST:************}}
    smtp:
      host: ${EMAIL_AIFAPIAO_SMTP_HOST:${EMAIL_AIFAPIAO_HOST:************}}
```

## 环境配置示例

### 开发环境
```bash
EMAIL_AIFAPIAO_HOST=************  # 公网IP
```

### 生产环境
```bash
EMAIL_AIFAPIAO_HOST=**********    # 内网IP
```

### 本地开发
```bash
EMAIL_AIFAPIAO_HOST=localhost     # 本地服务
```

## 配置优先级

配置的优先级从高到低：
1. 环境变量
2. 命令行参数
3. application.yml
4. bootstrap.yml
5. Nacos 配置中心

## 使用方法

配置完成后，代码中通过 `EmailConfigUtil` 获取配置：

```java
// 获取各服务的主机地址
String pop3Host = EmailConfigUtil.getAifapiaoPop3Host();
String imapHost = EmailConfigUtil.getAifapiaoImapHost();
String smtpHost = EmailConfigUtil.getAifapiaoSmtpHost();
```

## 注意事项

1. **向后兼容**：保留了原有的硬编码逻辑作为默认值
2. **灵活配置**：支持统一配置或分别配置各个服务
3. **环境隔离**：不同环境可以使用不同的邮件服务器
4. **配置验证**：启动时会在日志中显示实际使用的配置值
