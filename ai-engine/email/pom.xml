<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.guoranbot</groupId>
		<artifactId>ai-engine</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<groupId>com.guoranbot</groupId>
	<artifactId>email</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>
	<name>email</name>
	<description>Demo project for Spring Boot</description>

	    <properties>
        <java.version>11</java.version>
        <spring-cloud.version>Hoxton.SR1</spring-cloud.version>
        <!-- 使用bootstrap.yml中的application.name作为镜像名 -->
        <docker.image.name>email-service</docker.image.name>
    </properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/dnsjava/dnsjava -->
		<dependency>
			<groupId>dnsjava</groupId>
			<artifactId>dnsjava</artifactId>
			<version>2.1.9</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.sun.mail</groupId>
					<artifactId>jakarta.mail</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>-->
<!--		</dependency>-->
		<!-- Nacos服务发现和配置中心 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-stream-rabbit</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.activation</groupId>
			<artifactId>activation</artifactId>
			<version>1.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>1.5</version>
			<exclusions>
				<exclusion>
					<groupId>com.sun.mail</groupId>
					<artifactId>javax.mail</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.12.1</version>
		</dependency>
		<dependency>
			<groupId>com.guoranbot</groupId>
			<artifactId>common</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.guoranbot</groupId>
			<artifactId>jakarta.mail</artifactId>
			<version>1.6.4.1-SNAPSHOT</version>
		</dependency>
        <dependency>
            <groupId>com.guoranbot</groupId>
            <artifactId>oss</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
		<!-- 添加Micrometer依赖用于邮件清理监控指标 -->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<!-- Swagger API文档依赖 -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.9.2</version>
		</dependency>

    </dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-install-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.guoranbot.email.EmailApplication</mainClass>
				</configuration>
			</plugin>
			
			<!-- Jib插件 -->
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<configuration>
					<!-- 容器特定配置 -->
					<container>
						<ports>
							<port>8760</port>
						</ports>
						<jvmFlags>
							<jvmFlag>-server</jvmFlag>
							<jvmFlag>-Xms512m</jvmFlag>
							<jvmFlag>-Xmx1024m</jvmFlag>
							<jvmFlag>-XX:+UseG1GC</jvmFlag>
							<jvmFlag>-XX:MaxGCPauseMillis=100</jvmFlag>
							<jvmFlag>-XX:+UseStringDeduplication</jvmFlag>
							<jvmFlag>-Djava.security.egd=file:/dev/./urandom</jvmFlag>
							<jvmFlag>-Duser.timezone=Asia/Shanghai</jvmFlag>
							<jvmFlag>-javaagent:/dd-java-agent-v1.14.0-guance.jar</jvmFlag>
						</jvmFlags>
						<environment>
							<TZ>Asia/Shanghai</TZ>
							<DD_AGENT_HOST>datadog-agent</DD_AGENT_HOST>
							<DD_SERVICE>email-service</DD_SERVICE>
							<DD_ENV>production</DD_ENV>
						</environment>
						<mainClass>com.guoranbot.email.EmailApplication</mainClass>
					</container>
					<!-- APM Agent 文件复制 -->
					<extraDirectories>
						<paths>
							<path>
								<from>./</from>
								<includes>dd-java-agent-v1.14.0-guance.jar</includes>
								<into>/</into>
							</path>
						</paths>
					</extraDirectories>
				</configuration>
			</plugin>

<!--			<plugin>-->

<!--				<groupId>com.spotify</groupId>-->
<!--				<artifactId>dockerfile-maven-plugin</artifactId>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>default</id>-->
<!--						<goals>-->
<!--							&lt;!&ndash;如果package时不想用docker打包,就注释掉这个 goal build &ndash;&gt;-->
<!--&lt;!&ndash;							<goal>build</goal>&ndash;&gt;-->
<!--&lt;!&ndash;							<goal>push</goal>&ndash;&gt;-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->

<!--				<configuration>-->

<!--					<username>${docker.username}</username>-->
<!--					<password>${docker.password}</password>-->
<!--					<dockerfile>${pom.basedir}/Dockerfile</dockerfile>-->
<!--					<repository>${docker.repostory}/xbox/${project.artifactId}</repository>-->
<!--					<buildArgs>-->
<!--						&lt;!&ndash;提供参数向Dockerfile传递&ndash;&gt;-->
<!--						<JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>-->
<!--					</buildArgs>-->
<!--					<tag>latest</tag>-->

<!--				</configuration>-->
<!--			</plugin>-->
		</plugins>
	</build>

</project>
