package com.guoranbot.email.controller;

import com.guoranbot.common.dto.EmailValidationInfo;
import com.guoranbot.email.service.IEmailValidationService;
import com.guoranbot.email.util.HostType;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.mail.AuthenticationFailedException;
import javax.mail.MessagingException;
import java.nio.channels.UnsupportedAddressTypeException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/email")
@AllArgsConstructor
public class EmailSupportController {

    private final IEmailValidationService emailValidationService;

    @PostMapping("/validation")
    public boolean validation(@RequestParam String email, @RequestParam String password) {
        try {
            return emailValidationService.validate(email, password);
        } catch (Exception e) {
            return false;
        }
    }

    @PostMapping("/validation_info")
    public EmailValidationInfo validationInfo(@RequestParam String email, @RequestParam String password){

        EmailValidationInfo info = new EmailValidationInfo();

        try {
            boolean validate = emailValidationService.validate(email, password);
            if (validate==true){
                info.authAllSuccess();
                return info;
            }
        }catch (UnsupportedAddressTypeException e){
            info.setMessage(e.getMessage());
            info.setSupportEmail(false);
        }catch (AuthenticationFailedException e){
            String message = e.getMessage();
            info.setMessage(message);
            if (message.startsWith("LOGIN Login error")){
                info.setUserExist(false);
            }else if (message.startsWith("LOGIN Login error user suspended")){
                info.setProtocolOpen(false);
            }else if (message.startsWith("LOGIN Login error password error")){
                info.setAuthInfoPass(false);
            }else {
                info.setAuthInfoPass(false);
            }
        } catch (MessagingException e) {
            info.setAuthInfoPass(false);
            info.setMessage(e.getMessage());
        }

        return info;

    }

    @GetMapping("/support_domain")
    public List<String> supportEmail() {
        List<String> domains = Arrays.stream(HostType.values())
                .map(t -> t.getDomains())
                .flatMap(t -> t.stream())
                .distinct()
                .collect(Collectors.toList());
        return domains;
    }

    @GetMapping("/support_token")
    boolean supportToken(String email) {
        try {
            HostType hostTypeByEmail = HostType.getHostTypeByEmail(email);
            return hostTypeByEmail.isSupportToken();
        } catch (UnsupportedAddressTypeException e) {
            // 不支持的邮箱类型，返回false
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    @GetMapping("/host_type")
    public String hostType(@RequestParam String email) {
        try {
            HostType hostTypeByEmail = HostType.getHostTypeByEmail(email);
            return hostTypeByEmail.name();
        } catch (UnsupportedAddressTypeException e) {
            // 不支持的邮箱类型，返回null
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @GetMapping("/host_type/name")
    String hostTypeName(@RequestParam String email) {
        try {
            HostType hostTypeByEmail = HostType.getHostTypeByEmail(email);
            return hostTypeByEmail.getName();
        } catch (UnsupportedAddressTypeException e) {
            // 不支持的邮箱类型，返回null
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
