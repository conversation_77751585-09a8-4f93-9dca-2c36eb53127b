package com.guoranbot.email.service.impl;

import com.guoranbot.common.dto.EmailContext;
import com.guoranbot.common.dto.EmailFilterListDto;
import com.guoranbot.common.dto.EmailSyncRecordDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.guoranbot.common.dto.*;
import com.guoranbot.common.po.AccountUser;
import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.common.utils.DateUtil;
import com.guoranbot.email.MimeMessageParserPro;
import com.guoranbot.email.service.IEmailFetchService;
import com.guoranbot.email.service.IEmailRecordService;
import com.guoranbot.email.service.IEmailService;
import com.guoranbot.email.service.email.filter.MailboxTypeFilterServiceImpl;
import com.guoranbot.email.service.feign.IEmailDataService;
import com.guoranbot.email.service.feign.ISendMessage;
import com.guoranbot.email.task.AllParserCallback;
import com.guoranbot.email.task.EmailParseTask;
import com.guoranbot.email.task.GroupThreadPoolExecutor;
import com.guoranbot.email.util.AuthenticatorGenerator;
import com.guoranbot.email.util.EmailAuth;
import com.guoranbot.email.util.HostType;
import com.guoranbot.email.util.ProtocolType;
import com.guoranbot.email.util.*;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.pop3.POP3Folder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.mail.util.MimeMessageParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import java.nio.channels.UnsupportedAddressTypeException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.concurrent.*;

@Slf4j
@Service
public class EmailServiceImpl implements IEmailService {

    @Value("${email.subject.keywords.invoice}")
    protected String subjectInvoiceKeywords;

    @Autowired
    private IEmailDataService emailDataService;

    @Autowired
    private IEmailFetchService emailFetchService;

    @Autowired
    private ISendMessage sendMessage;

    @Autowired
    private MailboxTypeFilterServiceImpl mailboxTypeFilterService;

    @Autowired
    private IEmailRecordService emailRecordService;

    @Autowired
    private GroupThreadPoolExecutor groupThreadPoolExecutor;

    @Autowired
    private ExecutorService userEmailParsePool;

    @Value("${mail.parse.thread-pool.timeout}")
    private Long timeout;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public Boolean processEmail(EmailContext emailContext) {
        log.info("start to process mail openId:{},emial:{}", emailContext.getOpenId(), emailContext.getEmail());

        //校验同步记录
        if (!checkSyncRecord(emailContext)) {
            log.info("check sync record return false email:{}", emailContext.getEmail());
            return Boolean.FALSE;
        }

        //添加白名单
        addWhitelist(emailContext);
        //添加黑名单
        addBlacklist(emailContext);

        log.info("begin to auth email:{}", emailContext.getEmail());
        Folder folder = auth(emailContext, ProtocolType.IMAP, Folder.READ_ONLY);
        log.info("end to auth email:{}", emailContext.getEmail());

        executeParseTask(folder, emailContext);

        return Boolean.TRUE;
    }

    private void addWhitelist(EmailContext emailContext) {
        String whitelistKey = "email_from_whitelist";
        String whitelistData = redisTemplate.opsForValue().get(whitelistKey);
        List<EmailFilterListDto> filterList = null;
        if (StringUtils.isNotBlank(whitelistData)) {
            filterList = JSONArray.parseArray(whitelistData, EmailFilterListDto.class);
        }
        if (filterList == null || filterList.isEmpty()) {
            filterList = emailDataService.getFilterList(0, 1);
            redisTemplate.opsForValue().set(whitelistKey, JSON.toJSONString(filterList), 2, TimeUnit.HOURS);
        }
        emailContext.setFilterList(filterList);
        emailContext.setSubjectInvoiceKeywords(subjectInvoiceKeywords);
        log.debug("after add whitelist to email context:{}", emailContext);
    }

    private Folder auth(EmailContext emailContext, ProtocolType protocolType, Integer openType) {
        if(emailContext != null){
            log.info("begin to auth email:{},openId:{},pwd:{},protocolType:{},openType:{}", emailContext.getEmail(), emailContext.getOpenId(), emailContext.getPassword(),protocolType, openType);
        }
        if (emailContext == null || StringUtils.isBlank(emailContext.getEmail()) || StringUtils.isBlank(emailContext.getPassword())) {
            return null;
        }
        HostType hostType = HostType.getEnums(emailContext.getType());
        if (hostType == null) {
            try {
                hostType = HostType.getHostTypeByEmail(emailContext.getEmail());
            } catch (UnsupportedAddressTypeException e) {
                log.warn("Unsupported email type for email: {}, openId: {}", emailContext.getEmail(), emailContext.getOpenId());
                return null;
            } catch (Exception e) {
                log.error("Error getting host type for email: {}, openId: {}", emailContext.getEmail(), emailContext.getOpenId(), e);
                return null;
            }
        }
        if (hostType == null) {
            return null;
        }
        Authenticator authenticator = AuthenticatorGenerator.getAuthenticator(emailContext.getEmail(), emailContext.getPassword());
        return EmailAuth.auth(hostType, authenticator, protocolType, openType);
    }

    class MyAllParserCallback implements AllParserCallback {

        private final EmailContext emailContext;
        private final AtomicInteger messages = new AtomicInteger();
        private final Folder folder;


        public MyAllParserCallback(EmailContext emailContext, List<MimeMessage> messages, Folder folder) {
            this.emailContext = emailContext;
            this.folder = folder;
            this.messages.addAndGet(messages.size());
            log.info("all parse callBack,messages:{}", messages);
        }

        @Override
        public void success(String messageId) {
            log.info("messageId:{} parsed finish, last messageNum:{}", messageId, messages);
            messages.addAndGet(-1);
            if (allSuccess()) {
                try {
                    EmailAuth.closeFolder(folder);
                    folder.close();
                } catch (MessagingException e) {
                    log.error("", e);
                } finally {
                    updateSyncRecord(emailContext);
                }

            }
        }

        private boolean allSuccess() {
            return messages.get() <= 0;
        }
    }


    private void executeParseTask(Folder folder, EmailContext emailContext) {

        log.info("begin to fetch email openId:{},email:{}", emailContext.getOpenId(), emailContext.getEmail());
        List<Message> messages = emailFetchService.fetchEmail(emailContext, folder);
        log.info("end fetch email success openId:{},email:{}, size:{}", emailContext.getOpenId(), emailContext.getEmail(), messages == null ? 0 : messages.size());
        if (messages == null || messages.isEmpty()) {
            updateSyncRecord(emailContext);
            return;
        }

        int i = 0;
        MyAllParserCallback allParserCallback = new MyAllParserCallback(emailContext, messages.stream().map(m -> (MimeMessage) m).collect(Collectors.toList()), folder);
        for (Message message : messages) {
            try {
                log.info("start to handle message index:{},subject:{}", ++i, message.getSubject());
                MimeMessage mimeMessage = (MimeMessage) message;
                String messageUID = "";
                if (folder instanceof IMAPFolder) {
                    IMAPFolder imapFolder = (IMAPFolder) folder;
                    messageUID = imapFolder.getUID(message) + "";
                } else if (folder instanceof POP3Folder) {
                    POP3Folder pop3Folder = (POP3Folder) folder;
                    messageUID = pop3Folder.getUID(message);
                }
                groupThreadPoolExecutor.submitWithGroupId(emailContext.getOpenId(),
                        new EmailParseTask(emailContext, mimeMessage, messageUID, userEmailParsePool, timeout, emailRecordService, sendMessage, mailboxTypeFilterService, allParserCallback, groupThreadPoolExecutor));

            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    private Boolean checkSyncRecord(EmailContext emailContext) {

        EmailSyncRecordDto latestSyncRecord = emailContext.getEmailSyncRecordDto();
        log.info("=============latest sync record is null:{}", latestSyncRecord == null);

        Long defaultMinTime = getDefaultTime();
        if (latestSyncRecord == null) {
            //默认抓取最多18个月
            emailContext.setPreSyncTime(defaultMinTime);
            log.info("checkSyncRecord latestSyncRecord is null openId:{},email:{},time:{}", emailContext.getOpenId(), emailContext.getEmail(), emailContext.getPreSyncTime());
        } else {
            Integer emailCount = emailDataService.countEmail(emailContext.getOpenId(), emailContext.getEmail());
            if (emailCount < 20) {
                emailContext.setPreSyncTime(defaultMinTime);
            } else {
                emailContext.setPreSyncTime(latestSyncRecord.getSyncTime());
                Long emailMinTime = emailDataService.getEmailMinTime(emailContext.getOpenId(), emailContext.getEmail());
                emailContext.setEmailMinTime(emailMinTime);
                emailContext.setDefaultMinTime(defaultMinTime);
                log.info("checkSyncRecord latestSyncRecord is not null openId:{},email:{},time:{}", emailContext.getOpenId(), emailContext.getEmail(), emailContext.getPreSyncTime());
            }
        }
        return Boolean.TRUE;
    }

    private Boolean updateSyncRecord(EmailContext emailContext) {
        EmailSyncRecordDto syncRecordDto = EmailSyncRecordDto.builder()
                .id(emailContext.getSyncRecordId())
                .emailStatus(1)
                .build();
        int updateCount = emailDataService.updateSyncRecord(syncRecordDto);
        log.info("update sync record email:{},recordId:{}, status:1, result:{}", emailContext.getEmail(), emailContext.getSyncRecordId(), updateCount);
        return updateCount > 0;
    }

    private Long getDefaultTime() {
        Long defaultTime = DateUtil.getYearStartTime(DateUtil.getCurrentYear());
        int currentMonth = DateUtil.getCurrentMonth();
        if (currentMonth < 7) {
            defaultTime = DateUtil.getYearStartTime(DateUtil.getPrevYear());
        }
        return defaultTime;
    }


    @Override
    public Boolean setMessageSeen(Long syncRecordId) {
        if (syncRecordId != null) {
            List<String> invoiceMessageUIDs = emailDataService.getInvoiceMessageUIDs(syncRecordId);
            if (invoiceMessageUIDs == null || invoiceMessageUIDs.isEmpty()) {
                return Boolean.FALSE;
            }
            EmailSyncRecordDto syncRecord = emailDataService.getSyncRecordById(syncRecordId);
            if (syncRecord == null) {
                return Boolean.FALSE;
            }
            AccountUser accountUser = emailDataService.getAccountUser(syncRecord.getOpenId());
            if (accountUser == null || accountUser.getExtInfo() == null
                    || accountUser.getExtInfo().getEmails() == null) {
                return Boolean.FALSE;
            }
            List<AccountUserEmail> userEmails = accountUser.getExtInfo().getEmails();
            String emailPwd = "";
            for (AccountUserEmail userEmail : userEmails) {
                if (userEmail.getEmail().equals(syncRecord.getEmail())) {
                    emailPwd = userEmail.decodePassword();
                    break;
                }
            }
            if (StringUtils.isNotBlank(emailPwd)) {
                EmailContext emailContext = EmailContext.builder().email(syncRecord.getEmail()).password(emailPwd).build();
                Folder folder = auth(emailContext, ProtocolType.IMAP, Folder.READ_WRITE);
                List<Message> messages = emailFetchService.fetchEmailByIds(folder, invoiceMessageUIDs);
                if (messages != null && !messages.isEmpty()) {
                    messages.forEach(message -> {
                        try {
                            //邮件已被查看
                            message.setFlag(Flags.Flag.SEEN, Boolean.TRUE);
                            message.saveChanges();
                        } catch (MessagingException e) {
                            log.info("", e);
                        }
                    });
                }
                EmailAuth.closeFolder(folder);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean retryProcessEmail(EmailContext emailContext) {
        if (emailContext == null || emailContext.getEmailRecordDtos() == null || emailContext.getEmailRecordDtos().isEmpty()) {
            return Boolean.FALSE;
        }
        //添加白名单
        addWhitelist(emailContext);
        //添加黑名单
        addBlacklist(emailContext);


        Folder folder = auth(emailContext, ProtocolType.IMAP, Folder.READ_ONLY);
        if (folder == null) {
            log.info("retryProcessEmail auth failed openId:{},email:{}", emailContext.getOpenId(), emailContext.getEmail());
            return Boolean.FALSE;
        }

        List<EmailRecordDto> emailRecordDtos = emailContext.getEmailRecordDtos();
        Map<String, EmailRecordDto> messageRecordMap = new HashMap<>();
        Set<String> messageUIDSet = new HashSet<>();
        emailRecordDtos.forEach(record -> {
            messageUIDSet.add(record.getUid());
            messageRecordMap.put(record.getUid(), record);
        });


        List<Message> messages = emailFetchService.fetchEmailByIds(folder, new ArrayList<>(messageUIDSet));
        if (messages != null && !messages.isEmpty()) {
            for (Message message : messages) {
                if (message == null) {
                    continue;
                }
                String messageUID = EmailParseUtil.getMessageUID(folder, message);
                MimeMessage mimeMessage = (MimeMessage) message;
                try {
                    EmailRecordDto emailRecordDto = messageRecordMap.get(messageUID);
                    if (emailRecordDto == null) {
                        continue;
                    }
                    MimeMessageParser messageParser = null;
                    try {
                        messageParser = new MimeMessageParserPro(mimeMessage).parse();
                    } catch (Exception e) {
                        log.warn("MimeMessageParserPro parse error,mimeMessage:{}", mimeMessage, e);
                    }

                    if (messageParser == null) {
                        continue;
                    }

                    List<String> filePathList = emailRecordService.uploadEmailContent(emailContext, messageParser);
                    log.info("current message save success messageId:{}", mimeMessage.getMessageID());
                    MessageContent messageContent = EmailParseUtil.getMessageContent(emailRecordDto.getId(), emailRecordDto.getSyncRecordId(), messageUID, filePathList, emailContext, messageParser);


                    log.info("start to send parse email messageId:{}", mimeMessage.getMessageID());
                    sendMessage.sendParseEmail(messageContent);
                    log.info("end to send parse email messageId:{}", mimeMessage.getMessageID());
                } catch (Exception e) {
                    log.warn("retryProcessEmail error,emailContext:{},error:", emailContext, e);
                }
            }
        }
        return Boolean.TRUE;
    }

    private void addBlacklist(EmailContext emailContext) {
        List<String> blacklist = new ArrayList<>();
        blacklist.add("<EMAIL>");
        blacklist.add("<EMAIL>");
        emailContext.setFromBlackList(blacklist);
        log.debug("after add blacklist to email context:{}", emailContext);
    }

    @Override
    public Boolean deleteEmailsByUIDs(EmailContext emailContext, List<String> messageUIDs) {
        if (emailContext == null || messageUIDs == null || messageUIDs.isEmpty()) {
            log.warn("deleteEmailsByUIDs: invalid parameters, emailContext={}, messageUIDs={}", emailContext, messageUIDs);
            return Boolean.FALSE;
        }

        // 只处理AIFAPIAO邮箱
        if (!isAifapiaoEmail(emailContext.getEmail())) {
            log.warn("deleteEmailsByUIDs: not aifapiao email, email={}", emailContext.getEmail());
            return Boolean.FALSE;
        }

        log.info("deleteEmailsByUIDs: start deleting emails, email={}, messageUIDs={}", emailContext.getEmail(), messageUIDs);

        try {
            Folder folder = auth(emailContext, ProtocolType.IMAP, Folder.READ_WRITE);
            if (folder == null) {
                log.error("deleteEmailsByUIDs: auth failed, email={}", emailContext.getEmail());
                return Boolean.FALSE;
            }

            List<Message> messages = emailFetchService.fetchEmailByIds(folder, messageUIDs);
            if (messages == null || messages.isEmpty()) {
                log.info("deleteEmailsByUIDs: no messages found to delete, email={}", emailContext.getEmail());
                EmailAuth.closeFolder(folder);
                return Boolean.TRUE;
            }

            int deletedCount = 0;
            for (Message message : messages) {
                try {
                    message.setFlag(Flags.Flag.DELETED, true);
                    deletedCount++;
                    log.debug("deleteEmailsByUIDs: marked message for deletion, messageId={}", message.getMessageNumber());
                } catch (MessagingException e) {
                    log.error("deleteEmailsByUIDs: failed to mark message for deletion, messageId={}, error={}", 
                             message.getMessageNumber(), e.getMessage());
                }
            }

            // 提交删除操作
            folder.expunge();
            EmailAuth.closeFolder(folder);

            log.info("deleteEmailsByUIDs: completed, email={}, deleted={}/{}", emailContext.getEmail(), deletedCount, messages.size());

            // 标记邮件为已清理状态
            emailDataService.markEmailsAsClearedByUIDs(messageUIDs);

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("deleteEmailsByUIDs: unexpected error, email={}, error={}", emailContext.getEmail(), e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    @Override
    public Boolean batchDeleteEmails(EmailContext emailContext, List<String> messageUIDs, int batchSize) {
        if (emailContext == null || messageUIDs == null || messageUIDs.isEmpty() || batchSize <= 0) {
            log.warn("batchDeleteEmails: invalid parameters, emailContext={}, messageUIDs={}, batchSize={}", 
                     emailContext, messageUIDs, batchSize);
            return Boolean.FALSE;
        }

        // 只处理AIFAPIAO邮箱
        if (!isAifapiaoEmail(emailContext.getEmail())) {
            log.warn("batchDeleteEmails: not aifapiao email, email={}", emailContext.getEmail());
            return Boolean.FALSE;
        }

        log.info("batchDeleteEmails: start batch deleting emails, email={}, totalCount={}, batchSize={}", 
                 emailContext.getEmail(), messageUIDs.size(), batchSize);

        boolean overallSuccess = true;
        int totalDeleted = 0;
        
        for (int i = 0; i < messageUIDs.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, messageUIDs.size());
            List<String> batchUIDs = messageUIDs.subList(i, endIndex);
            
            log.info("batchDeleteEmails: processing batch {}/{}, size={}", 
                     (i / batchSize) + 1, (messageUIDs.size() + batchSize - 1) / batchSize, batchUIDs.size());
            
            try {
                Boolean batchResult = deleteEmailsByUIDs(emailContext, batchUIDs);
                if (batchResult) {
                    totalDeleted += batchUIDs.size();
                } else {
                    overallSuccess = false;
                    log.error("batchDeleteEmails: batch failed, batchUIDs={}", batchUIDs);
                }
                
                // 批次间暂停，避免过度负载
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.warn("batchDeleteEmails: interrupted during batch processing", e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("batchDeleteEmails: batch processing error, batchUIDs={}, error={}", batchUIDs, e.getMessage(), e);
                overallSuccess = false;
            }
        }

        log.info("batchDeleteEmails: completed, email={}, totalDeleted={}, success={}", 
                 emailContext.getEmail(), totalDeleted, overallSuccess);
        
        return overallSuccess;
    }

    @Override
    public Boolean deleteProcessedEmails(String emailAddress, String emailPassword, int days, boolean isSuccessOnly) {
        if (StringUtils.isBlank(emailAddress) || StringUtils.isBlank(emailPassword) || days < 0) {
            log.warn("deleteProcessedEmails: invalid parameters, emailAddress={}, days={}, isSuccessOnly={}", 
                     emailAddress, days, isSuccessOnly);
            return Boolean.FALSE;
        }

        // 只处理AIFAPIAO邮箱
        if (!isAifapiaoEmail(emailAddress)) {
            log.warn("deleteProcessedEmails: not aifapiao email, emailAddress={}", emailAddress);
            return Boolean.FALSE;
        }

        log.info("deleteProcessedEmails: start deleting processed emails, emailAddress={}, days={}, isSuccessOnly={}", 
                 emailAddress, days, isSuccessOnly);

        try {
            // 获取要清理的邮件列表
            List<EmailRecordDto> emailsToDelete = emailDataService.getEmailsForCleanupByEmail(emailAddress, days, isSuccessOnly);
            if (emailsToDelete == null || emailsToDelete.isEmpty()) {
                log.info("deleteProcessedEmails: no emails to delete, emailAddress={}", emailAddress);
                return Boolean.TRUE;
            }

            // 提取messageUIDs
            List<String> messageUIDs = emailsToDelete.stream()
                .map(EmailRecordDto::getUid)
                .filter(uid -> StringUtils.isNotBlank(uid))
                .collect(Collectors.toList());

            if (messageUIDs.isEmpty()) {
                log.info("deleteProcessedEmails: no valid messageUIDs found, emailAddress={}", emailAddress);
                return Boolean.TRUE;
            }

            // 构建EmailContext
            EmailContext emailContext = EmailContext.builder()
                .email(emailAddress)
                .password(emailPassword)
                .build();

            // 批量删除邮件
            Boolean deleteResult = batchDeleteEmails(emailContext, messageUIDs, 50);
            
            log.info("deleteProcessedEmails: completed, emailAddress={}, processed={}, success={}", 
                     emailAddress, messageUIDs.size(), deleteResult);
            
            return deleteResult;
        } catch (Exception e) {
            log.error("deleteProcessedEmails: unexpected error, emailAddress={}, error={}", emailAddress, e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 检查是否是AIFAPIAO邮箱
     */
    private boolean isAifapiaoEmail(String email) {
        return StringUtils.isNotBlank(email) && email.toLowerCase().endsWith("@aifapiao.com");
    }

}
