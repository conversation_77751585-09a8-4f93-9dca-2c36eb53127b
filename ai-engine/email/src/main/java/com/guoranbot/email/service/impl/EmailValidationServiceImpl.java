package com.guoranbot.email.service.impl;

import com.guoranbot.email.service.IEmailValidationService;
import com.guoranbot.email.util.AuthenticatorGenerator;
import com.guoranbot.email.util.EmailAuth;
import com.guoranbot.email.util.HostType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.mail.*;
import java.nio.channels.UnsupportedAddressTypeException;

@Slf4j
@Service
public class EmailValidationServiceImpl implements IEmailValidationService {


    @Override
    public boolean validate(String email, String password) throws MessagingException {

        try {
            return connect(email, password);
        }catch (Exception e){
            log.warn("connect email:{} fail",email,e);
            throw e;
        }
    }

    public static boolean connect(String email, String password) throws MessagingException {
        // 创建Session实例对象
        HostType hostTypeByEmail;
        try {
            hostTypeByEmail = HostType.getHostTypeByEmail(email);
        } catch (UnsupportedAddressTypeException e) {
            log.warn("Unsupported email type for email: {}", email);
            throw new MessagingException("Unsupported email type: " + email, e);
        } catch (Exception e) {
            log.error("Error getting host type for email: {}", email, e);
            throw new MessagingException("Error getting host type for email: " + email, e);
        }

        Authenticator authenticator = AuthenticatorGenerator.getAuthenticator(email, password);
        Session session = Session.getInstance(hostTypeByEmail.getProperties(), authenticator);
        // 创建IMAP协议的Store对象
        Store store = session.getStore("imap");
        // 连接邮件服务器
        store.connect();
        Folder inbox = store.getFolder("INBOX");
        EmailAuth.checkNeteaseImap(hostTypeByEmail,inbox);
        inbox.open(Folder.READ_ONLY);
        Message[] messages = inbox.getMessages();
        return true;

    }

}
