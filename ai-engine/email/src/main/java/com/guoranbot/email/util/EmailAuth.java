package com.guoranbot.email.util;

import com.sun.mail.imap.IMAPFolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import java.util.HashMap;
import java.util.Map;

import static com.guoranbot.common.po.AccountUserEmail.decodePass;

public class EmailAuth {

    private static Logger logger= LoggerFactory.getLogger(EmailAuth.class);

    public static Folder auth(HostType hostType, Authenticator authenticator,Integer openType) {
        return auth(hostType,authenticator,null,openType);
    }

    public static Folder auth(HostType hostType, Authenticator authenticator, ProtocolType protocolType,Integer openType) {
        Session session = Session.getInstance(hostType.getProperties(), authenticator);
        logger.info("enter into auth email:{}, hostType:{}, protocolType:{}", authenticator.toString(), hostType.getName(), protocolType != null ? protocolType.getCode() : "default");
        Folder folder = null;
        try {
            Store store = protocolType == null ? session.getStore() : session.getStore(protocolType.getCode());
            store.connect();
            folder = store.getFolder("INBOX");// 获取收件箱
            checkNeteaseImap(hostType, folder);
            folder.open(openType==null?Folder.READ_ONLY:openType);
        } catch (Exception e) {
            logger.error("auth error:",e);
        }
        return folder;
    }

    public static void checkNeteaseImap(HostType hostType,Folder folder){
        if(hostType!=HostType.NETEASE_163&&hostType!=HostType.NETEASE_126&&hostType!=HostType.NETEASE_YEAH){
            return;
        }
        if (folder instanceof IMAPFolder) {
            Map<String,String> clientParams = new HashMap<>();
            clientParams.put("GUID", "FUTONG");
            IMAPFolder imapFolder = (IMAPFolder)folder;
            //javamail中使用id命令有校验checkOpened, 所以要去掉id方法中的checkOpened();
            try {
                imapFolder.id(clientParams);
            } catch (MessagingException e) {
                logger.info("",e);
            }
        }
    }

    public static void closeFolder(Folder folder){
        try {
            if(folder!=null){
                Store store = folder.getStore();
                if(store!=null){
                    store.close();
                }
            }
        } catch (MessagingException e) {
            logger.info("",e);
        }
    }

    public static void main(String[] args) {
        String email = "<EMAIL>";
        String password = decodePass("WWMyYy04LmczZQ==");
        HostType hostType = HostType.getHostTypeByEmail(email);
        Authenticator authenticator = AuthenticatorGenerator.getAuthenticator(email, "Yc2c-8.g3e");
        Folder folder = EmailAuth.auth(hostType, authenticator, ProtocolType.IMAP, 1);

        System.out.println("xxx ->> " + folder);
    }
}
