package com.guoranbot.email.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 邮件配置工具类
 * 用于读取邮件服务器配置信息
 */
@Component
public class EmailConfigUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmailConfigUtil.class);

    @Value("${email.aifapiao.host:************}")
    private String aifapiaoHost;

    @Value("${email.aifapiao.pop3.host:${email.aifapiao.host:************}}")
    private String aifapiaoPop3Host;

    @Value("${email.aifapiao.imap.host:${email.aifapiao.host:************}}")
    private String aifapiaoImapHost;

    @Value("${email.aifapiao.smtp.host:${email.aifapiao.host:************}}")
    private String aifapiaoSmtpHost;

    private static EmailConfigUtil instance;

    @PostConstruct
    public void init() {
        instance = this;
        LOGGER.info("EmailConfigUtil initialized - AIFAPIAO hosts: POP3={}, IMAP={}, SMTP={}", 
                   aifapiaoPop3Host, aifapiaoImapHost, aifapiaoSmtpHost);
    }

    /**
     * 获取AIFAPIAO POP3服务器地址
     */
    public static String getAifapiaoPop3Host() {
        return instance != null ? instance.aifapiaoPop3Host : "************";
    }

    /**
     * 获取AIFAPIAO IMAP服务器地址
     */
    public static String getAifapiaoImapHost() {
        return instance != null ? instance.aifapiaoImapHost : "************";
    }

    /**
     * 获取AIFAPIAO SMTP服务器地址
     */
    public static String getAifapiaoSmtpHost() {
        return instance != null ? instance.aifapiaoSmtpHost : "************";
    }

    /**
     * 获取AIFAPIAO通用服务器地址（向后兼容）
     */
    public static String getAifapiaoHost() {
        return instance != null ? instance.aifapiaoHost : "************";
    }
}
