package com.guoranbot.email.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.xbill.DNS.*;

import java.nio.channels.UnsupportedAddressTypeException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务器种类：提供了网易和腾讯的企业邮箱和谷歌 后期有需要可以扩展
 */
public enum HostType {

    NETEASE_163(0, "163邮箱",true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop.163.com", "imap.163.com", "smtp.163.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("163.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }

    },
    NETEASE_126(1, "126邮箱", true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop.126.com", "imap.126.com", "smtp.126.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("126.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    NETEASE_YEAH(2, "yeah.net邮箱", true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop.yeah.net", "imap.yeah.net", "smtp.yeah.net");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("yeah.net");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    TENCENT_QQ(3, "腾讯QQ邮箱", true) {
        @Override
        public Properties getProperties() {
            return getPropertiesByQQ("pop.qq.com", "imap.qq.com", "smtp.qq.com");
        }

        @Override
        public List<String> getDomains() {
            return Arrays.asList("qq.com","foxmail.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    TENCENT_EXMAIL(4, "腾讯企业邮", true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop.exmail.qq.com", "imap.exmail.qq.com", "smtp.exmail.qq.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.emptyList();
        }

        @Override
        public List<String> getMX() {
            return Arrays.asList("mxbiz2.qq.com.", "mxbiz1.qq.com.");
        }
    },
    ALIYUN(5, "阿里云个人邮箱", false) {
        @Override
        public Properties getProperties() {
            return getProperties("pop3.aliyun.com", "imap.aliyun.com", "smtp.aliyun.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("aliyun.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    ALIYUN_EXMAIL(6, "阿里云企业邮", false) {
        @Override
        public Properties getProperties() {
            return getProperties("pop3.mxhichina.com", "imap.mxhichina.com", "smtp.mxhichina.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.emptyList();
        }

        @Override
        public List<String> getMX() {
            return Arrays.asList("mxn.mxhichina.com.", "mxw.mxhichina.com.");
        }
    },
    NETEASE_EXMAIL(7, "网易企业邮箱", true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop.qiye.163.com", "imap.qiye.163.com", "smtp.qiye.163.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.emptyList();
        }

        @Override
        public List<String> getMX() {
            return Arrays.asList("mx.qiye.163.com.", "mx2.qiye.163.com.","mx.ym.163.com.");
        }
    },
    SINA(8, "新浪邮箱", true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop.sina.com", "imap.sina.com", "smtp.sina.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("sina.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    SINA_EXMAIL(9, "新浪企业邮箱", false) {
        @Override
        public Properties getProperties() {
            return getProperties("pop3.sina.com", "imap.sina.com", "smtp.sina.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.emptyList();
        }

        @Override
        public List<String> getMX() {
            return Arrays.asList("mx.sina.net.", "mx.sinanet.com.", "mx.entmail.sina.com.");
        }
    },
    GMAIL(10, "谷歌", false) {
        //需要开启ssl，并开启允许安全低的应用访问
        @Override
        public Properties getProperties() {
            Properties pros = getProperties("pop.gmail.com", "imap.gmail.com", "smtp.gmail.com");
            pros.put("mail.imap.port", 993);
            pros.put("mail.imap.ssl.enable", true);
            return pros;
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("gmail.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    HOTMAIL(11, "微软邮箱", false) {
        @Override
        public Properties getProperties() {
            Properties pros = getProperties("outlook.office365.com", "outlook.office365.com", "smtp.office365.com");
            pros.put("mail.imap.port", 993);
            pros.put("mail.imap.ssl.enable", true);
            //imap 993 pop 995 smtp 587
            return pros;
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("hotmail.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },
    SOHU(12, "搜狐邮箱", true) {
        @Override
        public Properties getProperties() {
            return getProperties("pop3.sohu.com", "imap.sohu.com", "smtp.sohu.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("sohu.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }

    },
    YAHOO(13,"雅虎邮箱",false){
        @Override
        public Properties getProperties() {
            return getProperties("pop.mail.yahoo.com","imap.mail.yahoo.com","smtp.mail.yahoo.com");
        }

        @Override
        public List<String> getDomains() {
            return Collections.singletonList("yahoo.com");
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    },

    KWZG(14,"iRedMail",false){
        @Override
        public Properties getProperties() {
            Properties properties = getProperties("mx.kuaiwenzhuge.com", "mx.kuaiwenzhuge.com", "mx.kuaiwenzhuge.com");
            properties.put("mail.imap.starttls.enable", "true");
            return properties;
        }

        @Override
        public List<String> getDomains() {
            return Collections.emptyList();
        }

        @Override
        public List<String> getMX() {
            return Arrays.asList("mx.kuaiwenzhuge.com");
        }
    },

    AIFAPIAO(15,"aifapiao",false){
        @Override
        public Properties getProperties() {
            // 从配置文件读取邮件服务器地址
            String pop3Host = EmailConfigUtil.getAifapiaoPop3Host();
            String imapHost = EmailConfigUtil.getAifapiaoImapHost();
            String smtpHost = EmailConfigUtil.getAifapiaoSmtpHost();

            Properties properties = getProperties(pop3Host, imapHost, smtpHost);
            return properties;
        }

        @Override
        public List<String> getDomains() {
            return Collections.emptyList();
        }

        @Override
        public List<String> getMX() {
            return Collections.emptyList();
        }
    };

    /**
     * 是否支持授权码方式
     */
    private final boolean supportToken;

    public abstract Properties getProperties();

    public abstract List<String> getDomains();

    public abstract List<String> getMX();

    private Integer code;
    private String name;

    private static final Logger LOGGER = LoggerFactory.getLogger(HostType.class);

    HostType(Integer code, String name, boolean supportToken) {
        this.code = code;
        this.name = name;
        this.supportToken = supportToken;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public boolean isSupportToken() {
        return supportToken;
    }

    public static HostType getEnums(Integer code) {
        for (HostType value : HostType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static Properties getProperties(String pop3, String imap, String smtp) {
        Properties defaults = new Properties();
        defaults.put("mail.pop3.host", pop3);
        defaults.put("mail.imap.host", imap);

        defaults.put("mail.smtp.host", smtp);
        defaults.put("mail.store.protocol", "imap");
//        defaults.put("mail.imap.ssl.enable", "true"); // 启用IMAP SSL

        return defaults;
    }

    public static Properties getPropertiesByQQ(String pop3, String imap, String smtp) {
        Properties defaults = getProperties(pop3, imap, smtp);
        defaults.put("mail.imap.ssl.enable", "true"); // 启用IMAP SSL
        return defaults;
    }

    public static HostType getHostTypeByEmail(String email) {
        if (email.endsWith("@qq.com")||email.endsWith("@foxmail.com")) {
            return TENCENT_QQ;
        } else if (email.endsWith("@163.com")) {
            return NETEASE_163;
        } else if (email.endsWith("@126.com")) {
            return NETEASE_126;
        } else if (email.endsWith("@yeah.net")) {
            return NETEASE_YEAH;
        } else if (email.endsWith("@sina.com")) {
            return SINA;
        } else if (email.endsWith("@sohu.com")) {
            return SOHU;
        } else if (email.endsWith("@gmail.com")) {
            return GMAIL;
        } else if (email.endsWith("@hotmail.com")) {
            return HOTMAIL;
        } else if (email.endsWith("@aliyun.com")) {
            return ALIYUN;
        } else if (email.endsWith("@guoranbot.com")) {
            return TENCENT_EXMAIL;
        }else if (email.endsWith("@kuaiwenzhuge.com")){
            return KWZG;
        }else if (email.endsWith("@aifapiao.com")){
            return AIFAPIAO;
        }

        // 解析域名MX记录,映射到对应主机类型
        try {
            Record[] records = new Lookup(emailDomain(email), Type.MX).run();
            if (records != null && records.length != 0) {
                List<String> mxs = Arrays.stream(records).map(record -> ((MXRecord) record).getTarget().toString()).collect(Collectors.toList());
                for (HostType hostType : values()) {
                    List<String> mx = hostType.getMX();
                    if (mx.stream().anyMatch(m -> mxs.contains(m))) {
                        return hostType;
                    }
                }
            }

        } catch (TextParseException e) {
            LOGGER.warn("email domain:[{}] parse dns mx records exception", email, e);
        }

        LOGGER.warn("Unsupported email address type: {}, domain: {}", email, emailDomain(email));
        throw new UnsupportedAddressTypeException();
    }

    public static HostType getHostTypeByEmailWithCache(String email, Cache cache) {

        String domain = emailDomain(email);
        String hostTypeName = (String) cache.get(domain).get();

        if (hostTypeName == null) {
            HostType hostTypeByEmail = getHostTypeByEmail(email);
            cache.put(domain, hostTypeByEmail.name());
            return hostTypeByEmail;
        }

        for (HostType type : values()) {
            if (type.name().equals(hostTypeName)) {
                return type;
            }
        }

        throw new IllegalStateException("cache content " + hostTypeName + " illegal for hostType");

//        cache.put();
    }

    public static String emailDomain(String email) {
        return email.substring(email.lastIndexOf("@") + 1);
    }


    public static void main(String[] args) throws TextParseException {
        Record[] records = new Lookup("gmail.com", Type.MX).run();
        for (int i = 0; i < records.length; i++) {
            MXRecord mx = (MXRecord) records[i];
            System.out.println("Host " + mx.getTarget() + " has preference " + mx.getPriority());
        }
    }
}
