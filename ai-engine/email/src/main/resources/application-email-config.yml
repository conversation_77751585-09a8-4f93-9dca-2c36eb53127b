# 邮件服务器配置示例文件
# 可以通过环境变量或配置中心覆盖这些配置

# ===== 邮件配置 =====
email:
  # AIFAPIAO邮件服务器配置
  aifapiao:
    # 通用主机地址（向后兼容）
    host: ${EMAIL_AIFAPIAO_HOST:************}
    
    # 具体服务配置
    pop3:
      host: ${EMAIL_AIFAPIAO_POP3_HOST:${EMAIL_AIFAPIAO_HOST:************}}
      port: ${EMAIL_AIFAPIAO_POP3_PORT:110}
      
    imap:
      host: ${EMAIL_AIFAPIAO_IMAP_HOST:${EMAIL_AIFAPIAO_HOST:************}}
      port: ${EMAIL_AIFAPIAO_IMAP_PORT:143}
      
    smtp:
      host: ${EMAIL_AIFAPIAO_SMTP_HOST:${EMAIL_AIFAPIAO_HOST:************}}
      port: ${EMAIL_AIFAPIAO_SMTP_PORT:25}

# ===== 环境配置示例 =====
---
# 开发环境配置
spring:
  profiles: dev
  
email:
  aifapiao:
    host: ************  # 测试环境公网IP

---
# 生产环境配置  
spring:
  profiles: prod
  
email:
  aifapiao:
    host: **********    # 生产环境内网IP

---
# 本地开发环境配置
spring:
  profiles: local
  
email:
  aifapiao:
    host: localhost     # 本地开发环境
