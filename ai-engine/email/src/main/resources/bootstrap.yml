# 服务端口配置
server:
  port: ${SERVER_PORT:8760}

spring:
  application:
    name: email-service

  profiles:
    active: dev
  cloud:
    nacos:
      config:
        shared-configs:
          - data-id: aifapiao-config.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: aifapiao-common-config.yaml
            group: DEFAULT_GROUP
            refresh: true
        server-addr: mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: af7cd89a-e994-4667-ba69-d95ebac4788a
        accessKey:
        secretKey:
      discovery:
        server-addr: mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848
        auto-register: true
        namespace: af7cd89a-e994-4667-ba69-d95ebac4788a
        register:
          enabled: true
          group-name: DEFAULT_GROUP

# ===== 邮件配置 =====
email:
  # AIFAPIAO邮件服务器配置
  aifapiao:
    # 通用主机地址（向后兼容）
    host: ${EMAIL_AIFAPIAO_HOST:************}
    # 具体服务配置
    pop3:
      host: ${EMAIL_AIFAPIAO_POP3_HOST:${EMAIL_AIFAPIAO_HOST:************}}
    imap:
      host: ${EMAIL_AIFAPIAO_IMAP_HOST:${EMAIL_AIFAPIAO_HOST:************}}
    smtp:
      host: ${EMAIL_AIFAPIAO_SMTP_HOST:${EMAIL_AIFAPIAO_HOST:************}}