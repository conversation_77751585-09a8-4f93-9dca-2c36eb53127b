<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.2.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.guoranbot</groupId>
    <artifactId>ai-engine</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>ai-engine</name>
    <description>service ai engine</description>
    <properties>
        <java.version>11</java.version>
        <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
        <!-- Docker 仓库地址 - 与 build-image.sh 保持一致 -->
        <docker.repository>ekb-repo.tencentcloudcr.com/xbox</docker.repository>
        <!-- Docker 认证信息 - 使用环境变量，参考 build-image.sh -->
        <!-- 默认值：用户名使用100012303395，密码/token使用环境变量 -->
        <docker.username>${env.DOCKER_REGISTRY_USERNAME}</docker.username>
        <docker.password>${env.DOCKER_REGISTRY_PASSWORD}</docker.password>
        <!-- 备用token认证方式 -->
        <docker.token>${env.DOCKER_REGISTRY_TOKEN}</docker.token>
        <!-- 镜像标签，可以通过 -Ddocker.tag=xxx 参数传递，默认使用git分支名 -->
        <docker.tag>${git.branch}</docker.tag>
        <!-- 镜像名称，默认使用artifactId，子模块可根据application.name覆盖 -->
        <docker.image.name>${project.artifactId}</docker.image.name>
        <!-- Git相关属性，会被git-commit-id-plugin填充 -->
        <git.branch>develop</git.branch>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <!-- 加入Nacos Discovery Client 依赖 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>2.3.2</version>
        </dependency>

        <!-- JDK 11 Missing Modules -->
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.62</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.9.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.15</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>11</source>
                        <target>11</target>
                        <!-- 启用编译器并行处理 -->
                        <fork>true</fork>
                        <meminitial>512m</meminitial>
                        <maxmem>2048m</maxmem>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>1.4.10</version>
                </plugin>
                <!-- Git信息插件 -->
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>4.9.10</version>
                    <executions>
                        <execution>
                            <id>get-the-git-infos</id>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                            <phase>initialize</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <generateGitPropertiesFile>false</generateGitPropertiesFile>
                        <includeOnlyProperties>
                            <includeOnlyProperty>^git.branch$</includeOnlyProperty>
                            <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
                        </includeOnlyProperties>
                    </configuration>
                </plugin>
                <!-- Jib插件配置 -->
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>3.4.0</version>
                    <configuration>
                        <!-- 基础镜像配置 -->
                        <from>
                            <image>ekb-repo.tencentcloudcr.com/library/runtime-openjdk:11</image>
                            <platforms>
                                <platform>
                                    <architecture>amd64</architecture>
                                    <os>linux</os>
                                </platform>
                            </platforms>
                        </from>
                        <to>
                            <image>${docker.repository}/${docker.image.name}</image>
                            <tags>
                                <tag>latest</tag>
                                <tag>${docker.tag}</tag>
                                <tag>${project.version}</tag>
                            </tags>
                            <auth>
                                <!-- 优先使用 DOCKER_REGISTRY_USERNAME，如果未设置则使用默认值 -->
                                <username>${docker.username}</username>
                                <!-- 优先使用 DOCKER_REGISTRY_PASSWORD，如果未设置则尝试使用 DOCKER_REGISTRY_TOKEN -->
                                <password>${docker.password}</password>
                            </auth>
                        </to>
                        <!-- 容器配置 -->
                        <container>
                            <jvmFlags>
                                <jvmFlag>-server</jvmFlag>
                                <jvmFlag>-Xms512m</jvmFlag>
                                <jvmFlag>-Xmx2048m</jvmFlag>
                                <jvmFlag>-XX:+UseG1GC</jvmFlag>
                                <jvmFlag>-XX:MaxGCPauseMillis=100</jvmFlag>
                                <jvmFlag>-XX:+UseStringDeduplication</jvmFlag>
                                <jvmFlag>-Djava.security.egd=file:/dev/./urandom</jvmFlag>
                                <jvmFlag>-Duser.timezone=Asia/Shanghai</jvmFlag>
                                <jvmFlag>-javaagent:/dd-java-agent-v1.14.0-guance.jar</jvmFlag>
                            </jvmFlags>
                            <environment>
                                <TZ>Asia/Shanghai</TZ>
                                <DD_AGENT_HOST>datadog-agent</DD_AGENT_HOST>
                                <DD_ENV>production</DD_ENV>
                            </environment>
                            <ports>
                                <port>8080</port>
                            </ports>
                            <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        </container>
                        <!-- APM Agent 文件复制 -->
                        <extraDirectories>
                            <paths>
                                <path>
                                    <from>./</from>
                                    <includes>dd-java-agent-v1.14.0-guance.jar</includes>
                                    <into>/</into>
                                </path>
                            </paths>
                        </extraDirectories>
                    </configuration>
                </plugin>
                <!-- Maven 并行构建配置 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <parallel>methods</parallel>
                        <threadCount>4</threadCount>
                        <forkCount>2</forkCount>
                        <reuseForks>true</reuseForks>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <!-- 在根项目中激活git-commit-id-plugin -->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>email</module>
        <module>email-parser</module>
        <module>pdf-parser</module>
        <module>message</module>
        <module>common</module>
        <module>client</module>
        <module>datasource</module>
        <module>oss</module>
        <module>image</module>
        <module>classify</module>
        <module>enterprise-info</module>
        <module>analysis</module>
        <module>scheduler</module>
        <module>card-import</module>
        <module>sms</module>
        <module>dasource-listen</module>
        <module>customize-email-datasource</module>
        <module>toc-web</module>
    </modules>
    <repositories>
        <!-- 阿里云Maven仓库作为主要仓库 -->
        <repository>
            <id>aliyun-releases</id>
            <name>aliyun-releases</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
        <!-- 中央仓库作为备用 -->
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo1.maven.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!-- 企业仓库（如果认证可用） -->
        <repository>
            <id>repo</id>
            <url>https://repo.ekuaibao.com/repository/xbox-group/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>
    
    <!-- Docker认证配置Profiles -->
    <profiles>
        <!-- 默认用户名profile：如果未设置DOCKER_REGISTRY_USERNAME则使用默认值 -->
        <profile>
            <id>default-docker-username</id>
            <activation>
                <property>
                    <name>!env.DOCKER_REGISTRY_USERNAME</name>
                </property>
            </activation>
            <properties>
                <docker.username>100012303395</docker.username>
            </properties>
        </profile>
        
        <!-- Token认证profile：如果设置了DOCKER_REGISTRY_TOKEN但未设置DOCKER_REGISTRY_PASSWORD -->
        <profile>
            <id>docker-token-auth</id>
            <activation>
                <property>
                    <name>env.DOCKER_REGISTRY_TOKEN</name>
                </property>
            </activation>
            <properties>
                <docker.password>${env.DOCKER_REGISTRY_TOKEN}</docker.password>
            </properties>
        </profile>
        
        <!-- 密码认证profile：如果设置了DOCKER_REGISTRY_PASSWORD则优先使用 -->
        <profile>
            <id>docker-password-auth</id>
            <activation>
                <property>
                    <name>env.DOCKER_REGISTRY_PASSWORD</name>
                </property>
            </activation>
            <properties>
                <docker.password>${env.DOCKER_REGISTRY_PASSWORD}</docker.password>
            </properties>
        </profile>
    </profiles>
    
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>https://repo.ekuaibao.com/repository/xbox-release/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>https://repo.ekuaibao.com/repository/xbox-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
