#!/bin/bash

# 快速修复 APM Agent 问题
# 统一使用短文件名：dd-java-agent-guance.jar

set -e

echo "🔧 开始修复 APM Agent 配置问题..."

# 服务列表
SERVICES=(
    "email" "email-parser" "pdf-parser" "datasource"
    "message" "analysis" "classify" "enterprise-info" "card-import"
    "sms" "dasource-listen" "customize-email-datasource" "scheduler" "toc-web"
)

# 1. 重命名根目录的文件
if [[ -f "dd-java-agent-v1.14.0-guance.jar" ]] && [[ ! -f "dd-java-agent-guance.jar" ]]; then
    cp "dd-java-agent-v1.14.0-guance.jar" "dd-java-agent-guance.jar"
    echo "✅ 根目录：创建短文件名副本"
fi

# 2. 处理各个服务
for service in "${SERVICES[@]}"; do
    if [[ -d "$service" ]]; then
        echo "🔄 处理服务: $service"
        
        # 重命名服务目录中的文件
        if [[ -f "$service/dd-java-agent-v1.14.0-guance.jar" ]] && [[ ! -f "$service/dd-java-agent-guance.jar" ]]; then
            cp "$service/dd-java-agent-v1.14.0-guance.jar" "$service/dd-java-agent-guance.jar"
            echo "  ✅ 创建短文件名副本"
        fi
        
        # 更新pom.xml配置
        if [[ -f "$service/pom.xml" ]]; then
            # 更新 extraDirectories 中的文件名
            sed -i.bak 's/dd-java-agent-v1.14.0-guance\.jar/dd-java-agent-guance.jar/g' "$service/pom.xml"
            echo "  ✅ 更新pom.xml配置"
            rm -f "$service/pom.xml.bak"
        fi
    fi
done

# 3. 更新根目录pom.xml
if [[ -f "pom.xml" ]]; then
    sed -i.bak 's/dd-java-agent-v1.14.0-guance\.jar/dd-java-agent-guance.jar/g' "pom.xml"
    echo "✅ 更新根目录pom.xml"
    rm -f "pom.xml.bak"
fi

echo ""
echo "🎉 APM Agent 配置修复完成！"
echo ""
echo "📋 修复内容："
echo "- 统一使用短文件名：dd-java-agent-guance.jar"
echo "- 更新所有pom.xml中的文件引用"
echo "- JVM参数已匹配：-javaagent:/dd-java-agent-v1.14.0-guance.jar"
echo ""
echo "✅ 建议测试："
echo "   ./build-with-jib.sh eureka" 