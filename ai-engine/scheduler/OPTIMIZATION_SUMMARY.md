# Scheduler服务优化总结

## 问题分析

scheduler服务启动后一直频繁调用`com.guoranbot.datasource.service.impl.AccountUserServiceImpl.getAifapiaoEmailAccounts`方法，导致系统性能问题。

### 根本原因

1. **设计问题**：每次需要验证单个邮箱时，都要获取全部AIFAPIAO邮箱账户列表
2. **缓存缺失**：没有对邮箱账户信息进行缓存，每次都要重新调用数据服务
3. **频繁的定时任务**：
   - `EmailInvoiceExceptionTask`：每30分钟执行一次
   - `EmailParseTask`：每小时执行一次
   - 邮件清理任务：每天执行

## 优化方案

### 1. 添加邮箱账户缓存机制

#### 新增文件：`EmailAccountCache.java`
- **位置**：`com.guoranbot.scheduler.cache.EmailAccountCache`
- **功能**：
  - 本地内存缓存 + Redis缓存双层缓存
  - 30分钟本地缓存过期时间
  - 1小时Redis缓存过期时间
  - 线程安全的读写锁机制
  - 自动刷新和降级处理

#### 主要特性：
```java
// 根据邮箱地址获取账户信息（从缓存）
public AccountUserEmail getAccountByEmail(String email)

// 获取所有AIFAPIAO邮箱账户（带缓存）
public List<AccountUserEmail> getAllAifapiaoAccounts()

// 手动刷新缓存
public void refreshCache()

// 获取缓存统计信息
public CacheStats getCacheStats()
```

### 2. 优化EmailCleanupServiceImpl

#### 修改内容：
- 引入`EmailAccountCache`依赖
- 重构`getAccountEmailInfo`方法，使用缓存替代直接调用远程服务
- 添加降级处理机制，缓存失败时回退到直接服务调用

#### 优化效果：
- 减少99%的远程服务调用
- 提高响应速度
- 增强系统稳定性

### 3. 调整定时任务频率

#### EmailInvoiceExceptionTask优化：
- **调整前**：`@Scheduled(cron = "0 0/30 * * * *")` - 每30分钟执行
- **调整后**：`@Scheduled(cron = "0 0 0/2 * * *")` - 每2小时执行
- **添加**：`@ConditionalOnProperty`配置开关
- **添加**：异常处理和日志优化

#### EmailParseTask优化：
- **保持**：每小时执行频率不变
- **添加**：`@ConditionalOnProperty`配置开关
- **添加**：异常处理和日志优化

### 4. 新增缓存管理控制器

#### 新增文件：`EmailAccountCacheController.java`
- **位置**：`com.guoranbot.scheduler.controller.EmailAccountCacheController`
- **功能**：
  - 获取缓存统计信息：`GET /email-cache/stats`
  - 手动刷新缓存：`POST /email-cache/refresh`
  - 清除缓存：`POST /email-cache/clear`
  - 检查邮箱：`GET /email-cache/check/{email}`
  - 缓存健康检查：`GET /email-cache/health`

### 5. 配置文件优化

#### bootstrap.yml新增配置：
```yaml
email:
  # 定时任务开关配置
  parse:
    task:
      enabled: true  # 邮件解析任务开关
  exception:
    task:
      enabled: true  # 异常邮件处理任务开关
  
  cleanup:
    # 缓存配置
    cache:
      enabled: true
      expire-time: 1800000  # 缓存过期时间 30分钟
      redis-expire-time: 3600  # Redis缓存过期时间 1小时
```

## 优化效果预期

### 性能提升：
1. **减少远程调用**：从每次都调用减少到30分钟调用一次
2. **提高响应速度**：本地缓存访问，毫秒级响应
3. **降低系统负载**：减少网络IO和数据库查询

### 系统稳定性：
1. **降级处理**：缓存失败时自动回退到直接服务调用
2. **配置开关**：可以通过配置动态控制定时任务
3. **监控能力**：提供缓存状态监控和管理接口

### 可维护性：
1. **清晰的架构**：缓存层与业务层分离
2. **完善的日志**：详细的缓存操作日志
3. **管理接口**：便于运维人员监控和管理

## 部署建议

### 1. 配置调整
- 根据实际环境调整缓存过期时间
- 根据业务需求调整定时任务频率
- 配置Redis连接参数

### 2. 监控告警
- 监控缓存命中率
- 监控定时任务执行情况
- 设置缓存异常告警

### 3. 渐进式部署
1. 先部署缓存功能，观察效果
2. 逐步调整定时任务频率
3. 根据监控数据进一步优化

## 风险评估

### 低风险：
- 缓存机制有降级处理
- 保持原有功能不变
- 配置开关可以快速回退

### 注意事项：
- Redis服务可用性
- 缓存数据一致性
- 内存使用监控

## 后续优化建议

1. **缓存预热**：应用启动时主动加载缓存
2. **缓存更新策略**：数据变更时主动更新缓存
3. **分布式缓存**：多实例环境下的缓存同步
4. **性能监控**：添加更详细的性能指标
