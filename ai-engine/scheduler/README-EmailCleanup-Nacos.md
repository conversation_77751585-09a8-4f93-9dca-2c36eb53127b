# 邮件清理服务 - Nacos版本说明

## 概述
本项目的邮件清理服务使用Nacos作为服务发现和配置中心。本文档说明了在Nacos环境下的配置、部署和故障排除。

## 架构说明

### 服务架构
```
┌─────────────────┐    Nacos服务发现    ┌─────────────────┐
│ scheduler-service│ ──────────────────→ │   data-service  │
│                 │                     │                 │
│ 邮件清理调度     │ ←────────────────── │ 数据查询服务     │
│ 健康检查        │    Feign调用        │ 邮箱账户管理     │
└─────────────────┘                     └─────────────────┘
         │                                       │
         ▼                                       ▼
┌─────────────────┐                     ┌─────────────────┐
│   email-service │                     │    数据库       │
│                 │                     │                 │
│ 邮件删除执行     │                     │ 用户邮箱数据     │
└─────────────────┘                     └─────────────────┘
```

### Nacos配置
- **服务器**: `mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848`
- **命名空间**: `af7cd89a-e994-4667-ba69-d95ebac4788a`
- **组**: `DEFAULT_GROUP`

## 关键组件

### 1. Feign客户端配置
- **文件**: `DataServiceFeignConfig.java`
- **功能**: 配置重试、超时、错误处理
- **特性**: 支持Nacos服务发现、负载均衡

### 2. 服务健康检查
- **文件**: `ServiceHealthChecker.java`
- **接口**: `/health/*`
- **功能**: 检查依赖服务状态

### 3. Nacos服务发现检查
- **文件**: `NacosServiceDiscoveryChecker.java`
- **接口**: `/health/nacos`
- **功能**: 检查服务在Nacos中的注册状态

### 4. 诊断服务
- **文件**: `EmailCleanupDiagnosticService.java`
- **接口**: `/health/diagnostic`
- **功能**: 全面的问题诊断和修复建议

## API接口

### 健康检查接口
```bash
# 检查Nacos服务发现
GET /health/nacos

# 检查Data Service健康状态
GET /health/data-service

# 检查AIFAPIAO邮箱账户接口
GET /health/aifapiao-accounts

# 获取完整健康报告
GET /health/report

# 运行完整诊断
GET /health/diagnostic

# 检查整体健康状态
GET /health/overall
```

### 邮件清理接口
```bash
# 清理成功邮件
POST /email-cleanup/success?days=1

# 清理失败邮件
POST /email-cleanup/failed?days=3

# 清理指定邮箱
POST /email-cleanup/email?emailAddress=<EMAIL>&days=1&isSuccessOnly=true

# 预览清理
GET /email-cleanup/preview?emailAddress=<EMAIL>&days=1&isSuccessOnly=true
```

## 配置说明

### bootstrap.yml
```yaml
spring:
  application:
    name: scheduler-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDRESS:mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848}
        namespace: ${NACOS_NAMESPACE:af7cd89a-e994-4667-ba69-d95ebac4788a}
        register:
          enabled: true
          group-name: ${NACOS_GROUP:DEFAULT_GROUP}
```

### Feign配置
```yaml
feign:
  client:
    config:
      data-service:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: full
```

## 部署指南

### 1. 环境要求
- Java 8+
- Spring Boot 2.x
- Spring Cloud Alibaba
- Nacos Server

### 2. 配置检查
```bash
# 检查Nacos连接
telnet mse-30e0a982-p.nacos-ans.mse.aliyuncs.com 8848

# 验证服务注册
curl -X GET "http://localhost:8767/health/nacos"
```

### 3. 启动顺序
1. 确保Nacos服务器运行
2. 启动data-service
3. 启动scheduler-service
4. 验证服务发现

## 故障排除

### 常见问题
1. **404错误**: 检查服务注册和接口路径
2. **连接超时**: 检查网络和Nacos配置
3. **服务发现失败**: 验证命名空间和组配置

### 诊断步骤
1. 运行诊断接口：`GET /health/diagnostic`
2. 检查Nacos控制台
3. 查看服务日志
4. 验证网络连接

### 日志关键字
- `Data service unavailable`
- `404 reading IDataInfoService`
- `Nacos naming client not ready`
- `No available server for client`

## 监控和告警

### 关键指标
- 服务注册状态
- 接口调用成功率
- 响应时间
- 错误率

### 告警配置
```yaml
email:
  cleanup:
    monitoring:
      enabled: true
      alerts:
        failure-rate-threshold: 0.1
        slow-execution-threshold: 1800000
```

## 最佳实践

1. **服务发现**
   - 使用统一的命名空间和组
   - 配置合适的健康检查间隔
   - 监控服务注册状态

2. **错误处理**
   - 实现Fallback机制
   - 配置合理的重试策略
   - 记录详细的错误日志

3. **性能优化**
   - 调整连接池大小
   - 配置合适的超时时间
   - 使用负载均衡

## 相关文档
- [Nacos故障排除指南](./nacos-troubleshooting.md)
- [测试验证指南](./test-email-cleanup.md)
- [Spring Cloud Alibaba文档](https://spring-cloud-alibaba-group.github.io/github-pages/hoxton/en-us/index.html)
