# Feign Bean冲突快速修复指南

## 当前错误
```
Parameter 0 of method feignBuilder in org.springframework.cloud.openfeign.FeignClientsConfiguration required a single bean, but 3 were found:
- dataServiceFeignRetryer
- defaultFeignRetryer  
- emailHistoryAnalysisFeignRetryer
```

## 问题原因
Spring Cloud OpenFeign在创建`feignBuilder`时发现了多个`Retryer`类型的Bean，无法确定使用哪一个。

## 已实施的解决方案

### 1. 移除@Configuration注解
将Feign配置类从全局配置改为特定配置：

```java
// 修改前
@Configuration
public class DataServiceFeignConfig { ... }

// 修改后  
public class DataServiceFeignConfig { ... }
```

### 2. 移除Bean名称
让每个配置类中的Bean只对特定的Feign客户端生效：

```java
// 修改前
@Bean("dataServiceFeignRetryer")
public Retryer feignRetryer() { ... }

// 修改后
@Bean
public Retryer feignRetryer() { ... }
```

### 3. 配置作用域
现在每个Feign客户端使用独立的配置：

```java
@FeignClient(name = "data-service", 
             contextId = "dataInfoService",
             configuration = DataServiceFeignConfig.class)
public interface IDataInfoService { ... }

@FeignClient(name = "data-service", 
             contextId = "emailHistoryAnalysisService", 
             configuration = EmailHistoryAnalysisFeignConfig.class)
public interface IEmailHistoryAnalysisService { ... }
```

## 验证修复

### 1. 检查Bean冲突
```bash
curl -X GET "http://localhost:8767/health/bean-conflicts"
```

### 2. 启动应用
查看启动日志，确认没有Bean冲突错误。

### 3. 测试Feign客户端
```bash
# 测试数据服务
curl -X GET "http://localhost:8767/health/data-service"

# 测试邮件历史分析服务
curl -X POST "http://localhost:8767/batch-cleanup/get-plan"
```

## 如果问题仍然存在

### 临时解决方案1：启用Bean覆盖
```yaml
spring:
  main:
    allow-bean-definition-overriding: true
```

### 临时解决方案2：使用全局默认配置
如果特定配置不工作，可以临时使用全局配置：

```yaml
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 30000
        retryer: com.guoranbot.scheduler.config.GlobalFeignConfiguration.DefaultRetryer
```

### 调试步骤
1. **查看Bean创建日志**：
   ```
   grep -i "bean" application.log | grep -i "retryer\|options"
   ```

2. **检查配置加载**：
   ```
   grep -i "feign" application.log | grep -i "config"
   ```

3. **验证contextId**：
   确保每个FeignClient都有唯一的contextId

## 最佳实践总结

### ✅ 正确的做法
1. **不使用@Configuration**：Feign配置类不应该是全局配置
2. **独立配置**：每个Feign客户端使用独立的配置类
3. **唯一contextId**：每个FeignClient设置唯一的contextId
4. **条件Bean**：全局默认Bean使用@ConditionalOnMissingBean

### ❌ 避免的做法
1. **全局Bean冲突**：多个配置类定义相同类型的Bean
2. **重复contextId**：多个FeignClient使用相同的contextId
3. **混合配置**：在全局和特定配置中都定义相同的Bean

## 配置结构图
```
Application Context
├── GlobalFeignConfiguration (全局默认，条件生效)
│   ├── @ConditionalOnMissingBean Request.Options
│   ├── @ConditionalOnMissingBean Retryer  
│   └── @ConditionalOnMissingBean Logger.Level
│
├── IDataInfoService (contextId: dataInfoService)
│   └── DataServiceFeignConfig (特定配置)
│       ├── Request.Options
│       ├── Retryer
│       ├── Logger.Level
│       └── ErrorDecoder
│
└── IEmailHistoryAnalysisService (contextId: emailHistoryAnalysisService)
    └── EmailHistoryAnalysisFeignConfig (特定配置)
        ├── Request.Options
        ├── Retryer
        ├── Logger.Level
        └── ErrorDecoder
```

## 监控和维护
- 定期运行Bean冲突检查
- 监控应用启动日志
- 验证Feign客户端功能
- 更新文档和配置
