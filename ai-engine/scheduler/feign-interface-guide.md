# 如何将REST接口发布为Feign接口

## 概述
本文档详细说明了如何将`@GetMapping("/aifapiao-accounts")`接口发布为Feign接口，以及整个过程的最佳实践。

## 完整流程

### 1. 服务提供方（data-service）- 发布REST接口

#### 步骤1: 创建Controller
```java
@Api(tags = "数据源邮件清理接口")
@RestController
@RequestMapping("/email/cleanup")  // 基础路径
public class EmailCleanupController {

    @Autowired
    private IAccountUserService accountUserService;

    /**
     * 获取所有AIFAPIAO邮箱账户
     */
    @ApiOperation(value = "获取所有AIFAPIAO邮箱账户")
    @GetMapping("/aifapiao-accounts")  // 完整路径: /email/cleanup/aifapiao-accounts
    public ResponseEntity<List<AccountUserEmail>> getAifapiaoEmailAccounts() {
        List<AccountUserEmail> accounts = accountUserService.getAifapiaoEmailAccounts();
        return ResponseEntity.ok(accounts);
    }
}
```

#### 步骤2: 确保服务注册到Nacos
```yaml
# data-service的bootstrap.yml
spring:
  application:
    name: data-service  # 重要：服务名称
  cloud:
    nacos:
      discovery:
        server-addr: mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848
        namespace: af7cd89a-e994-4667-ba69-d95ebac4788a
        register:
          enabled: true
          group-name: DEFAULT_GROUP
```

### 2. 服务消费方（scheduler-service）- 创建Feign接口

#### 步骤1: 定义Feign接口
```java
@FeignClient(
    name = "data-service",           // 必须与服务提供方的spring.application.name一致
    contextId = "dataInfoService",   // 避免Bean名称冲突
    configuration = DataServiceFeignConfig.class,  // 自定义配置
    fallback = IDataInfoServiceFallback.class      // 降级处理
)
public interface IDataInfoService {

    /**
     * 获取AIFAPIAO邮箱账户
     * 路径必须与服务提供方完全一致
     */
    @GetMapping("/email/cleanup/aifapiao-accounts")
    List<AccountUserEmail> getAifapiaoEmailAccounts();
}
```

#### 步骤2: 创建Feign配置类
```java
@Configuration
public class DataServiceFeignConfig {

    @Bean
    public Request.Options feignOptions() {
        return new Request.Options(
            10000, // 连接超时时间
            30000  // 读取超时时间
        );
    }

    @Bean
    public Retryer feignRetryer() {
        return new Retryer.Default(100, 1000, 3);
    }

    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean
    @LoadBalanced  // 支持Nacos负载均衡
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

#### 步骤3: 创建Fallback实现
```java
@Component
public class IDataInfoServiceFallback implements IDataInfoService {

    @Override
    public List<AccountUserEmail> getAifapiaoEmailAccounts() {
        log.error("Data service unavailable, cannot get AIFAPIAO email accounts");
        return new ArrayList<>();
    }
}
```

### 3. 关键配置点

#### 路径映射规则
```
服务提供方:
@RequestMapping("/email/cleanup")  +  @GetMapping("/aifapiao-accounts")
= 完整路径: /email/cleanup/aifapiao-accounts

服务消费方:
@GetMapping("/email/cleanup/aifapiao-accounts")  // 必须完全一致
```

#### 服务名称映射
```
data-service (Nacos中的服务名)
    ↓
@FeignClient(name = "data-service")
    ↓
通过Nacos服务发现找到实际的服务实例
```

### 4. 验证步骤

#### 步骤1: 验证服务注册
```bash
# 检查data-service是否注册到Nacos
curl -X GET "http://localhost:8767/health/nacos"
```

#### 步骤2: 直接测试REST接口
```bash
# 直接访问data-service的接口
curl -X GET "http://data-service-ip:8764/email/cleanup/aifapiao-accounts"
```

#### 步骤3: 测试Feign调用
```bash
# 通过scheduler-service测试Feign调用
curl -X GET "http://localhost:8767/health/aifapiao-accounts"
```

### 5. 常见问题和解决方案

#### 问题1: 404 Not Found
**原因**: 路径不匹配
**解决方案**:
```java
// 确保路径完全一致
// 服务提供方
@RequestMapping("/email/cleanup")
@GetMapping("/aifapiao-accounts")

// 服务消费方
@GetMapping("/email/cleanup/aifapiao-accounts")  // 完整路径
```

#### 问题2: 服务发现失败
**原因**: 服务名称不匹配或未注册
**解决方案**:
```yaml
# 确保服务名称一致
spring:
  application:
    name: data-service  # 与@FeignClient(name = "data-service")一致
```

#### 问题3: 超时错误
**原因**: 默认超时时间太短
**解决方案**:
```java
@Bean
public Request.Options feignOptions() {
    return new Request.Options(10000, 30000);  // 增加超时时间
}
```

### 6. 最佳实践

#### 1. 统一接口定义
```java
// 在common模块定义接口，服务提供方和消费方共享
public interface EmailCleanupApi {
    @GetMapping("/email/cleanup/aifapiao-accounts")
    List<AccountUserEmail> getAifapiaoEmailAccounts();
}

// 服务提供方实现
@RestController
public class EmailCleanupController implements EmailCleanupApi {
    // 实现方法
}

// 服务消费方使用
@FeignClient(name = "data-service")
public interface IDataInfoService extends EmailCleanupApi {
    // 继承接口，自动获得方法定义
}
```

#### 2. 版本管理
```java
@RequestMapping("/v1/email/cleanup")  // 添加版本号
public class EmailCleanupController {
    
    @GetMapping("/aifapiao-accounts")
    public ResponseEntity<List<AccountUserEmail>> getAifapiaoEmailAccounts() {
        // 实现
    }
}
```

#### 3. 错误处理
```java
@GetMapping("/email/cleanup/aifapiao-accounts")
@ResponseBody
public Result<List<AccountUserEmail>> getAifapiaoEmailAccounts() {
    try {
        List<AccountUserEmail> accounts = service.getAifapiaoEmailAccounts();
        return Result.success(accounts);
    } catch (Exception e) {
        return Result.error("获取邮箱账户失败: " + e.getMessage());
    }
}
```

### 7. 调试技巧

#### 启用Feign日志
```yaml
logging:
  level:
    com.guoranbot.scheduler.service.feign: DEBUG
    feign: DEBUG
```

#### 使用Actuator监控
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,httptrace
```

#### 添加请求拦截器
```java
@Bean
public RequestInterceptor requestInterceptor() {
    return template -> {
        template.header("X-Request-Source", "scheduler-service");
        log.info("Feign request: {} {}", template.method(), template.url());
    };
}
```

## 总结

将REST接口发布为Feign接口的关键点：
1. **路径一致性**: Feign接口的路径必须与REST接口完全一致
2. **服务名称**: @FeignClient的name必须与服务提供方的spring.application.name一致
3. **服务注册**: 确保服务正确注册到Nacos
4. **错误处理**: 实现Fallback机制和合适的错误处理
5. **配置优化**: 设置合适的超时时间和重试策略
