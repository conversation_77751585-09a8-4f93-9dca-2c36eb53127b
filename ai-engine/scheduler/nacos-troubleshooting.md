# Nacos服务发现故障排除指南

## 概述
本文档提供了在使用Nacos作为服务发现时，排除邮件清理服务404错误的详细步骤。

## Nacos环境信息
- **Nacos服务器**: `mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848`
- **命名空间**: `af7cd89a-e994-4667-ba69-d95ebac4788a`
- **组**: `DEFAULT_GROUP`
- **服务名**: `data-service`

## 故障排除步骤

### 1. 检查Nacos连接
```bash
# 测试Nacos服务器连接
telnet mse-30e0a982-p.nacos-ans.mse.aliyuncs.com 8848

# 或使用curl测试
curl -X GET "http://mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848/nacos/v1/ns/operator/metrics"
```

### 2. 访问Nacos控制台
```
URL: http://mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848/nacos
```

在控制台中检查：
- 服务管理 → 服务列表
- 查找命名空间：`af7cd89a-e994-4667-ba69-d95ebac4788a`
- 查找服务：`data-service`

### 3. 验证服务注册状态
使用API检查服务注册：
```bash
# 获取服务列表
curl -X GET "http://mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=100&namespaceId=af7cd89a-e994-4667-ba69-d95ebac4788a"

# 获取data-service实例
curl -X GET "http://mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848/nacos/v1/ns/instance/list?serviceName=data-service&namespaceId=af7cd89a-e994-4667-ba69-d95ebac4788a"
```

### 4. 检查服务配置
验证scheduler-service的Nacos配置：

**bootstrap.yml**:
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848
        namespace: af7cd89a-e994-4667-ba69-d95ebac4788a
        register:
          enabled: true
          group-name: DEFAULT_GROUP
```

**data-service的配置**:
```yaml
spring:
  application:
    name: data-service
  cloud:
    nacos:
      discovery:
        server-addr: mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848
        namespace: af7cd89a-e994-4667-ba69-d95ebac4788a
        register:
          enabled: true
          group-name: DEFAULT_GROUP
```

### 5. 检查网络和防火墙
```bash
# 从scheduler-service容器/服务器测试到data-service的连接
# 假设data-service实例在10.0.0.100:8764
curl -X GET "http://10.0.0.100:8764/actuator/health"

# 测试具体的接口
curl -X GET "http://10.0.0.100:8764/email/cleanup/aifapiao-accounts"
```

### 6. 查看日志
**scheduler-service日志关键字**:
```
- "Nacos naming client not ready"
- "No available server for client"
- "Failed to get service instances"
- "data-service"
```

**data-service日志关键字**:
```
- "register service"
- "Nacos registry"
- "register instance"
```

## 常见问题和解决方案

### 问题1: data-service未注册到Nacos
**症状**: Nacos控制台看不到data-service
**解决方案**:
1. 检查data-service的Nacos配置
2. 确认data-service启动时没有错误
3. 检查网络连接到Nacos服务器

### 问题2: 服务注册了但实例不健康
**症状**: 服务列表中有data-service但实例状态为DOWN
**解决方案**:
1. 检查data-service的健康检查端点
2. 确认服务端口正确
3. 检查服务内部状态

### 问题3: 命名空间或组不匹配
**症状**: 两个服务都注册了但无法互相发现
**解决方案**:
1. 确认两个服务使用相同的命名空间
2. 确认两个服务使用相同的组
3. 重启服务使配置生效

### 问题4: 接口路径错误
**症状**: 服务发现正常但接口返回404
**解决方案**:
1. 确认接口路径：`/email/cleanup/aifapiao-accounts`
2. 检查Controller的RequestMapping
3. 验证接口是否真实存在

## 验证修复
修复后，使用以下命令验证：
```bash
# 1. 检查Nacos服务发现
curl -X GET "http://localhost:8767/health/nacos"

# 2. 运行完整诊断
curl -X GET "http://localhost:8767/health/diagnostic"

# 3. 测试邮件清理功能
curl -X POST "http://localhost:8767/email-cleanup/success?days=1"
```

## 监控建议
1. 设置Nacos服务注册状态监控
2. 监控服务实例健康状态
3. 设置接口调用成功率告警
4. 定期检查服务发现功能

## 联系支持
如果问题仍然存在，请提供：
1. scheduler-service和data-service的完整日志
2. Nacos控制台截图
3. 网络连接测试结果
4. 诊断接口的完整输出
