package com.guoranbot.scheduler;

import com.guoranbot.oss.OssAutoConfiguration;
import com.guoranbot.oss.OssTemplate;
import com.guoranbot.scheduler.config.ApplicationShutdownConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@EnableScheduling
@EnableFeignClients
@SpringBootApplication
public class SchedulerApplication {

	public static void main(String[] args) {
		// 添加JVM关闭钩子，确保资源正确清理
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			log.info("JVM关闭钩子触发，开始清理所有线程资源...");
			ApplicationShutdownConfig.cleanupAllThreads();
			log.info("JVM关闭钩子执行完成");
		}, "shutdown-cleanup-thread"));

		SpringApplication.run(SchedulerApplication.class, args);
	}

	@Bean
	public OssTemplate ossTemplate(OssAutoConfiguration ossAutoConfiguration, @Value("${aliyun.oss.bucketName}") String bucket){
		return new OssTemplate(ossAutoConfiguration.oss(),bucket);
	}
}
