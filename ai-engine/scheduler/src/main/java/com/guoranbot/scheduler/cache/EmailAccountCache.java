package com.guoranbot.scheduler.cache;

import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.scheduler.service.feign.IDataInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 邮箱账户缓存组件
 * 用于缓存AIFAPIAO邮箱账户信息，减少对数据服务的频繁调用
 */
@Slf4j
@Component
public class EmailAccountCache {

    private static final String CACHE_KEY = "aifapiao:email:accounts";
    private static final String LAST_UPDATE_KEY = "aifapiao:email:accounts:last_update";
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟缓存过期时间
    private static final long REDIS_CACHE_EXPIRE_TIME = 60 * 60; // Redis缓存1小时

    @Autowired
    private IDataInfoService dataInfoService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 本地内存缓存
    private final Map<String, AccountUserEmail> localEmailAccountMap = new ConcurrentHashMap<>();
    private volatile long lastLocalUpdateTime = 0;
    
    // 读写锁，保证缓存更新的线程安全
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private final ReentrantReadWriteLock.ReadLock readLock = lock.readLock();
    private final ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();

    /**
     * 初始化缓存
     */
    @PostConstruct
    public void init() {
        log.info("EmailAccountCache initializing...");
        try {
            refreshCache();
            log.info("EmailAccountCache initialized successfully with {} accounts", localEmailAccountMap.size());
        } catch (Exception e) {
            log.error("Failed to initialize EmailAccountCache", e);
        }
    }

    /**
     * 根据邮箱地址获取账户信息
     * @param email 邮箱地址
     * @return 账户信息，如果不存在返回null
     */
    public AccountUserEmail getAccountByEmail(String email) {
        if (email == null) {
            return null;
        }

        readLock.lock();
        try {
            // 检查本地缓存是否需要刷新
            if (needRefreshLocalCache()) {
                readLock.unlock();
                refreshCache();
                readLock.lock();
            }
            
            return localEmailAccountMap.get(email.toLowerCase());
        } finally {
            readLock.unlock();
        }
    }

    /**
     * 获取所有AIFAPIAO邮箱账户（带缓存）
     * @return 邮箱账户列表
     */
    @SuppressWarnings("unchecked")
    public List<AccountUserEmail> getAllAifapiaoAccounts() {
        try {
            // 先尝试从Redis获取
            List<AccountUserEmail> cachedAccounts = (List<AccountUserEmail>) redisTemplate.opsForValue().get(CACHE_KEY);
            Long lastUpdate = (Long) redisTemplate.opsForValue().get(LAST_UPDATE_KEY);
            
            if (cachedAccounts != null && lastUpdate != null && 
                (System.currentTimeMillis() - lastUpdate) < CACHE_EXPIRE_TIME) {
                log.debug("Retrieved {} accounts from Redis cache", cachedAccounts.size());
                return cachedAccounts;
            }
            
            // Redis缓存过期或不存在，从数据服务获取
            log.info("Redis cache expired or missing, fetching from data service");
            List<AccountUserEmail> accounts = dataInfoService.getAifapiaoEmailAccounts();
            
            if (accounts != null && !accounts.isEmpty()) {
                // 更新Redis缓存
                redisTemplate.opsForValue().set(CACHE_KEY, accounts, REDIS_CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
                redisTemplate.opsForValue().set(LAST_UPDATE_KEY, System.currentTimeMillis(), REDIS_CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.info("Updated Redis cache with {} accounts", accounts.size());
            }
            
            return accounts;
        } catch (Exception e) {
            log.error("Error getting accounts from cache, falling back to direct service call", e);
            return dataInfoService.getAifapiaoEmailAccounts();
        }
    }

    /**
     * 刷新缓存
     */
    public void refreshCache() {
        writeLock.lock();
        try {
            log.debug("Refreshing email account cache...");
            List<AccountUserEmail> accounts = getAllAifapiaoAccounts();
            
            if (accounts != null) {
                localEmailAccountMap.clear();
                for (AccountUserEmail account : accounts) {
                    if (account.getEmail() != null) {
                        localEmailAccountMap.put(account.getEmail().toLowerCase(), account);
                    }
                }
                lastLocalUpdateTime = System.currentTimeMillis();
                log.info("Email account cache refreshed with {} accounts", localEmailAccountMap.size());
            } else {
                log.warn("Failed to refresh cache: received null accounts list");
            }
        } catch (Exception e) {
            log.error("Error refreshing email account cache", e);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        writeLock.lock();
        try {
            localEmailAccountMap.clear();
            lastLocalUpdateTime = 0;
            redisTemplate.delete(CACHE_KEY);
            redisTemplate.delete(LAST_UPDATE_KEY);
            log.info("Email account cache cleared");
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 检查本地缓存是否需要刷新
     */
    private boolean needRefreshLocalCache() {
        return lastLocalUpdateTime == 0 || 
               (System.currentTimeMillis() - lastLocalUpdateTime) > CACHE_EXPIRE_TIME ||
               localEmailAccountMap.isEmpty();
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        readLock.lock();
        try {
            return new CacheStats(
                localEmailAccountMap.size(),
                lastLocalUpdateTime,
                System.currentTimeMillis() - lastLocalUpdateTime,
                !needRefreshLocalCache()
            );
        } finally {
            readLock.unlock();
        }
    }

    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final int accountCount;
        private final long lastUpdateTime;
        private final long cacheAge;
        private final boolean isValid;

        public CacheStats(int accountCount, long lastUpdateTime, long cacheAge, boolean isValid) {
            this.accountCount = accountCount;
            this.lastUpdateTime = lastUpdateTime;
            this.cacheAge = cacheAge;
            this.isValid = isValid;
        }

        public int getAccountCount() { return accountCount; }
        public long getLastUpdateTime() { return lastUpdateTime; }
        public long getCacheAge() { return cacheAge; }
        public boolean isValid() { return isValid; }

        @Override
        public String toString() {
            return String.format("CacheStats{accountCount=%d, lastUpdateTime=%d, cacheAge=%d, isValid=%s}", 
                               accountCount, lastUpdateTime, cacheAge, isValid);
        }
    }
}
