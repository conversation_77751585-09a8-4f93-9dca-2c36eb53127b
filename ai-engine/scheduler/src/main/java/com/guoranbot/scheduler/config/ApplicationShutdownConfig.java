package com.guoranbot.scheduler.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;

import javax.annotation.PreDestroy;
import java.lang.reflect.Field;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 应用关闭时的资源清理配置
 * 解决Spring Cloud InetUtils线程泄漏问题
 */
@Slf4j
@Configuration
public class ApplicationShutdownConfig implements DisposableBean {

    private final InetUtils inetUtils;

    public ApplicationShutdownConfig(InetUtils inetUtils) {
        this.inetUtils = inetUtils;
    }

    /**
     * 监听应用关闭事件
     */
    @EventListener
    public void handleContextClosed(ContextClosedEvent event) {
        log.info("应用关闭事件触发，开始清理资源...");
        cleanupInetUtilsThreads();
    }

    /**
     * PreDestroy注解确保在Bean销毁前执行
     */
    @PreDestroy
    public void preDestroy() {
        log.info("PreDestroy触发，开始清理InetUtils资源...");
        cleanupInetUtilsThreads();
    }

    /**
     * DisposableBean接口方法，Spring容器关闭时调用
     */
    @Override
    public void destroy() throws Exception {
        log.info("DisposableBean.destroy()触发，开始清理资源...");
        cleanupInetUtilsThreads();
    }

    /**
     * 清理InetUtils相关的线程池
     */
    private void cleanupInetUtilsThreads() {
        try {
            if (inetUtils != null) {
                // 通过反射获取InetUtils中的ExecutorService
                Field executorField = InetUtils.class.getDeclaredField("executor");
                executorField.setAccessible(true);
                ExecutorService executor = (ExecutorService) executorField.get(inetUtils);
                
                if (executor != null && !executor.isShutdown()) {
                    log.info("正在关闭InetUtils线程池...");
                    
                    // 优雅关闭线程池
                    executor.shutdown();
                    
                    // 等待任务完成，最多等待5秒
                    if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.warn("InetUtils线程池未能在5秒内正常关闭，强制关闭...");
                        executor.shutdownNow();
                        
                        // 再次等待，确保线程被中断
                        if (!executor.awaitTermination(2, TimeUnit.SECONDS)) {
                            log.error("InetUtils线程池强制关闭失败");
                        } else {
                            log.info("InetUtils线程池强制关闭成功");
                        }
                    } else {
                        log.info("InetUtils线程池正常关闭成功");
                    }
                }
            }
        } catch (NoSuchFieldException e) {
            log.warn("未找到InetUtils.executor字段，可能是Spring Cloud版本差异: {}", e.getMessage());
        } catch (IllegalAccessException e) {
            log.warn("无法访问InetUtils.executor字段: {}", e.getMessage());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("等待InetUtils线程池关闭时被中断: {}", e.getMessage());
        } catch (Exception e) {
            log.error("清理InetUtils线程时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理所有可能的线程泄漏
     */
    public static void cleanupAllThreads() {
        try {
            // 获取当前线程组
            ThreadGroup currentGroup = Thread.currentThread().getThreadGroup();
            ThreadGroup rootGroup = currentGroup;
            
            // 找到根线程组
            while (rootGroup.getParent() != null) {
                rootGroup = rootGroup.getParent();
            }
            
            // 获取所有活跃线程
            Thread[] threads = new Thread[rootGroup.activeCount() * 2];
            int count = rootGroup.enumerate(threads, true);
            
            // 查找并中断spring.cloud.inetutils线程
            for (int i = 0; i < count; i++) {
                Thread thread = threads[i];
                if (thread != null && thread.getName().contains("spring.cloud.inetutils")) {
                    log.info("发现InetUtils线程: {}, 状态: {}", thread.getName(), thread.getState());
                    
                    if (thread.isAlive() && !thread.isInterrupted()) {
                        log.info("中断InetUtils线程: {}", thread.getName());
                        thread.interrupt();
                        
                        // 等待线程结束
                        try {
                            thread.join(1000); // 等待1秒
                            if (thread.isAlive()) {
                                log.warn("InetUtils线程 {} 未能在1秒内结束", thread.getName());
                            } else {
                                log.info("InetUtils线程 {} 已成功结束", thread.getName());
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.error("等待线程结束时被中断: {}", e.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("清理线程时发生异常: {}", e.getMessage(), e);
        }
    }
}
