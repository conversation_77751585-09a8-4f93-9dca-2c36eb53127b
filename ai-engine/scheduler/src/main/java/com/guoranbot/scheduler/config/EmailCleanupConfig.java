package com.guoranbot.scheduler.config;

import com.guoranbot.scheduler.service.EmailCleanupMonitoringService;
import com.guoranbot.scheduler.service.IBatchEmailCleanupService;
import com.guoranbot.scheduler.task.BatchEmailCleanupTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 邮件清理配置类
 * 确保邮件清理相关的Bean正确创建
 */
@Slf4j
@Configuration
public class EmailCleanupConfig {

    @Autowired
    private IBatchEmailCleanupService batchCleanupService;

    @Autowired
    private EmailCleanupMonitoringService monitoringService;

    /**
     * 强制创建BatchEmailCleanupTask Bean
     * 解决@ConditionalOnProperty配置未生效的问题
     */
    @Bean
    @ConditionalOnMissingBean(BatchEmailCleanupTask.class)
    public BatchEmailCleanupTask batchEmailCleanupTask() {
        log.info("强制创建BatchEmailCleanupTask Bean");
        return new BatchEmailCleanupTask();
    }
}
