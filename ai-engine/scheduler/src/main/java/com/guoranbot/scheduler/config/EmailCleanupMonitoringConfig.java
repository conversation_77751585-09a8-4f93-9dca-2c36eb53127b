package com.guoranbot.scheduler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 邮箱清理监控配置类
 * 用于管理监控相关的配置参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "email.cleanup.monitoring")
public class EmailCleanupMonitoringConfig {

    /**
     * 是否启用监控
     */
    private boolean enabled = true;

    /**
     * 告警配置
     */
    private AlertConfig alerts = new AlertConfig();

    /**
     * 数据保留策略配置
     */
    private RetentionConfig retention = new RetentionConfig();

    /**
     * 通知配置
     */
    private NotificationConfig notification = new NotificationConfig();

    @Data
    public static class AlertConfig {
        /**
         * 失败率告警阈值 (0.0-1.0)
         */
        private double failureRateThreshold = 0.05;

        /**
         * 慢执行告警阈值 (毫秒)
         */
        private long slowExecutionThreshold = 900000; // 15分钟

        /**
         * 最大并发任务数
         */
        private int maxActiveTasks = 3;

        /**
         * 单次清理邮件数量上限
         */
        private int maxSingleCleanupEmails = 10000;

        /**
         * 告警检查间隔 (分钟)
         */
        private int checkIntervalMinutes = 5;
    }

    @Data
    public static class RetentionConfig {
        /**
         * 任务数据保留时间 (小时)
         */
        private int taskDataHours = 24;

        /**
         * 告警数据保留时间 (天)
         */
        private int alertDataDays = 7;

        /**
         * 指标数据保留时间 (天)
         */
        private int metricsDataDays = 30;
    }

    @Data
    public static class NotificationConfig {
        /**
         * 是否启用通知
         */
        private boolean enabled = true;

        /**
         * 通知渠道列表
         */
        private List<String> channels = List.of("console", "log", "dingtalk");

        /**
         * 通知接收人配置
         */
        private RecipientsConfig recipients = new RecipientsConfig();

        /**
         * 告警级别通知策略
         */
        private Map<String, SeverityRuleConfig> severityRules = Map.of(
                "CRITICAL", new SeverityRuleConfig(List.of("dingtalk", "sms", "email"), true),
                "HIGH", new SeverityRuleConfig(List.of("dingtalk", "email"), true),
                "MEDIUM", new SeverityRuleConfig(List.of("dingtalk"), false),
                "LOW", new SeverityRuleConfig(List.of("log"), false)
        );
    }

    @Data
    public static class RecipientsConfig {
        /**
         * 邮件接收人列表
         */
        private List<String> email = List.of("<EMAIL>", "<EMAIL>");

        /**
         * 钉钉配置
         */
        private DingTalkConfig dingtalk = new DingTalkConfig();

        /**
         * 短信接收人列表
         */
        private SmsConfig sms = new SmsConfig();
    }

    @Data
    public static class DingTalkConfig {
        /**
         * 钉钉Webhook URL
         */
        private String webhook;

        /**
         * 钉钉密钥
         */
        private String secret;

        /**
         * 钉钉用户ID列表
         */
        private List<String> userIds = List.of("user001", "user002");
    }

    @Data
    public static class SmsConfig {
        /**
         * 短信接收手机号列表
         */
        private List<String> phones = List.of("13800138000");
    }

    @Data
    public static class SeverityRuleConfig {
        /**
         * 通知渠道
         */
        private List<String> channels;

        /**
         * 是否立即发送
         */
        private boolean immediate;

        public SeverityRuleConfig() {}

        public SeverityRuleConfig(List<String> channels, boolean immediate) {
            this.channels = channels;
            this.immediate = immediate;
        }
    }

    /**
     * 获取告警阈值配置
     */
    public AlertConfig getAlerts() {
        return alerts;
    }

    /**
     * 获取数据保留配置
     */
    public RetentionConfig getRetention() {
        return retention;
    }

    /**
     * 获取通知配置
     */
    public NotificationConfig getNotification() {
        return notification;
    }

    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return enabled && 
               alerts.failureRateThreshold >= 0 && alerts.failureRateThreshold <= 1 &&
               alerts.slowExecutionThreshold > 0 &&
               alerts.maxActiveTasks > 0 &&
               alerts.maxSingleCleanupEmails > 0;
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format("监控配置 - 启用: %s, 失败率阈值: %.1f%%, 慢执行阈值: %d分钟, 最大并发: %d个, 单次最大邮件: %d封",
                enabled,
                alerts.failureRateThreshold * 100,
                alerts.slowExecutionThreshold / 60000,
                alerts.maxActiveTasks,
                alerts.maxSingleCleanupEmails);
    }
}
