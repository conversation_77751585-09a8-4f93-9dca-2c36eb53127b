package com.guoranbot.scheduler.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

/**
 * Feign Bean冲突解决器
 * 统一管理可能冲突的Bean定义
 */
@Configuration
public class FeignBeanConflictResolver {

    /**
     * 全局的负载均衡RestTemplate
     * 使用@Primary注解确保这个Bean优先被使用
     */
    @Bean("globalLoadBalancedRestTemplate")
    @Primary
    @LoadBalanced
    @ConditionalOnMissingBean(name = "globalLoadBalancedRestTemplate")
    public RestTemplate globalLoadBalancedRestTemplate() {
        return new RestTemplate();
    }

    /**
     * 普通的RestTemplate（不带负载均衡）
     */
    @Bean("globalRestTemplate")
    @ConditionalOnMissingBean(name = "globalRestTemplate")
    public RestTemplate globalRestTemplate() {
        return new RestTemplate();
    }
}
