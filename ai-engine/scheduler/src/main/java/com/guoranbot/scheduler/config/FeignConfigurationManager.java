package com.guoranbot.scheduler.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign配置管理器
 * 统一管理所有Feign客户端的通用配置，避免Bean名称冲突
 */
@Slf4j
@Configuration
public class FeignConfigurationManager {

    // 移除全局默认Bean，避免与Spring Cloud OpenFeign的默认配置冲突
    // 每个Feign客户端使用自己的配置类中定义的Bean

    /**
     * 全局Feign配置说明：
     *
     * 为了避免Bean冲突，我们不在全局范围创建Feign相关的Bean。
     * 每个Feign客户端都应该在自己的配置类中定义所需的Bean：
     *
     * - DataServiceFeignConfig: 为IDataInfoService提供配置
     * - EmailHistoryAnalysisFeignConfig: 为IEmailHistoryAnalysisService提供配置
     * - DataEmailFeignConfig: 为其他email相关服务提供配置
     *
     * 这样可以确保每个Feign客户端使用独立的配置，避免Bean冲突。
     */

    /**
     * 获取Bean冲突诊断信息
     */
    public static class BeanConflictInfo {
        public static void logBeanInfo() {
            log.info("=== Feign Bean Configuration Info ===");
            log.info("Spring Cloud OpenFeign provides default beans: feignRetryer, Request.Options, Logger.Level");
            log.info("Data Service Config: DataServiceFeignConfig (non-global)");
            log.info("Email History Analysis Config: EmailHistoryAnalysisFeignConfig (non-global)");
            log.info("Email Service Config: DataEmailFeignConfig (non-global)");
            log.info("Global Beans: globalLoadBalancedRestTemplate, globalRestTemplate");
            log.info("Note: Each Feign client uses its own configuration scope");
            log.info("=====================================");
        }
    }
}
