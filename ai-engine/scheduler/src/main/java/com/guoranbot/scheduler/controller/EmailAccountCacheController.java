package com.guoranbot.scheduler.controller;

import com.guoranbot.scheduler.cache.EmailAccountCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮箱账户缓存管理控制器
 * 提供缓存监控和管理功能
 */
@Api(tags = "邮箱账户缓存管理")
@RestController
@RequestMapping("/email-cache")
@Slf4j
public class EmailAccountCacheController {

    @Autowired
    private EmailAccountCache emailAccountCache;

    /**
     * 获取缓存统计信息
     */
    @ApiOperation(value = "获取缓存统计信息")
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        try {
            EmailAccountCache.CacheStats stats = emailAccountCache.getCacheStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("accountCount", stats.getAccountCount());
            response.put("lastUpdateTime", stats.getLastUpdateTime());
            response.put("cacheAge", stats.getCacheAge());
            response.put("isValid", stats.isValid());
            response.put("cacheAgeMinutes", stats.getCacheAge() / (1000 * 60));
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting cache stats", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get cache stats: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 手动刷新缓存
     */
    @ApiOperation(value = "手动刷新缓存")
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        try {
            long startTime = System.currentTimeMillis();
            emailAccountCache.refreshCache();
            long duration = System.currentTimeMillis() - startTime;
            
            EmailAccountCache.CacheStats stats = emailAccountCache.getCacheStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache refreshed successfully");
            response.put("refreshDuration", duration);
            response.put("accountCount", stats.getAccountCount());
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("Cache manually refreshed, duration: {}ms, accounts: {}", duration, stats.getAccountCount());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error refreshing cache", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to refresh cache: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 清除缓存
     */
    @ApiOperation(value = "清除缓存")
    @PostMapping("/clear")
    public ResponseEntity<Map<String, Object>> clearCache() {
        try {
            emailAccountCache.clearCache();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Cache cleared successfully");
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("Cache manually cleared");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error clearing cache", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to clear cache: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 检查指定邮箱是否在缓存中
     */
    @ApiOperation(value = "检查指定邮箱是否在缓存中")
    @GetMapping("/check/{email}")
    public ResponseEntity<Map<String, Object>> checkEmail(@PathVariable String email) {
        try {
            boolean exists = emailAccountCache.getAccountByEmail(email) != null;
            
            Map<String, Object> response = new HashMap<>();
            response.put("email", email);
            response.put("exists", exists);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error checking email in cache: {}", email, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("email", email);
            errorResponse.put("error", "Failed to check email: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取缓存健康状态
     */
    @ApiOperation(value = "获取缓存健康状态")
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getCacheHealth() {
        try {
            EmailAccountCache.CacheStats stats = emailAccountCache.getCacheStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("healthy", stats.isValid() && stats.getAccountCount() > 0);
            response.put("accountCount", stats.getAccountCount());
            response.put("cacheAge", stats.getCacheAge());
            response.put("isValid", stats.isValid());
            response.put("timestamp", System.currentTimeMillis());
            
            // 判断健康状态
            boolean isHealthy = stats.isValid() && stats.getAccountCount() > 0;
            if (isHealthy) {
                return ResponseEntity.ok(response);
            } else {
                response.put("warning", "Cache may be unhealthy");
                return ResponseEntity.status(503).body(response);
            }
        } catch (Exception e) {
            log.error("Error getting cache health", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("healthy", false);
            errorResponse.put("error", "Failed to get cache health: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
