package com.guoranbot.scheduler.controller;

import com.guoranbot.scheduler.service.feign.IEmailHistoryAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件历史分析测试控制器
 * 用于测试scheduler模块调用datasource模块的邮件历史分析服务
 */
@RestController
@RequestMapping("/test/email-analysis")
@Slf4j
public class EmailHistoryAnalysisTestController {

    @Autowired
    private IEmailHistoryAnalysisService emailHistoryAnalysisService;

    /**
     * 测试获取邮件统计报告
     */
    @GetMapping("/statistics")
    public Map<String, Object> testGetEmailStatistics() {
        log.info("测试调用邮件统计报告接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.getEmailStatisticsReport();
            log.info("邮件统计报告调用成功: {}", result);
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("邮件统计报告调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试获取时间分布
     */
    @GetMapping("/time-distribution")
    public Map<String, Object> testGetTimeDistribution() {
        log.info("测试调用邮件时间分布接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.getEmailTimeDistribution();
            log.info("邮件时间分布调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("邮件时间分布调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试获取状态分布
     */
    @GetMapping("/status-distribution")
    public Map<String, Object> testGetStatusDistribution() {
        log.info("测试调用邮件状态分布接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.getEmailStatusDistribution();
            log.info("邮件状态分布调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("邮件状态分布调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试获取类型分布
     */
    @GetMapping("/type-distribution")
    public Map<String, Object> testGetTypeDistribution() {
        log.info("测试调用邮件类型分布接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.getEmailTypeDistribution();
            log.info("邮件类型分布调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("邮件类型分布调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试评估清理影响
     */
    @GetMapping("/cleanup-impact/{months}")
    public Map<String, Object> testEvaluateCleanupImpact(@PathVariable Integer months) {
        log.info("测试调用清理影响评估接口，月数: {}", months);
        try {
            Map<String, Object> result = emailHistoryAnalysisService.evaluateCleanupImpact(months);
            log.info("清理影响评估调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("清理影响评估调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试生成清理计划
     */
    @GetMapping("/cleanup-plan")
    public Map<String, Object> testGenerateCleanupPlan() {
        log.info("测试调用生成清理计划接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.generateCleanupPlan();
            log.info("生成清理计划调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("生成清理计划调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试获取Top用户分布
     */
    @GetMapping("/top-users")
    public Map<String, Object> testGetTopUsersDistribution(@RequestParam(defaultValue = "10") Integer topN) {
        log.info("测试调用Top用户分布接口，TopN: {}", topN);
        try {
            List<Map<String, Object>> result = emailHistoryAnalysisService.getTopUsersEmailDistribution(topN);
            log.info("Top用户分布调用成功，返回{}条记录", result.size());
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("Top用户分布调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试获取Top发件人分布
     */
    @GetMapping("/top-senders")
    public Map<String, Object> testGetTopSendersDistribution(@RequestParam(defaultValue = "10") Integer topN) {
        log.info("测试调用Top发件人分布接口，TopN: {}", topN);
        try {
            List<Map<String, Object>> result = emailHistoryAnalysisService.getTopSendersDistribution(topN);
            log.info("Top发件人分布调用成功，返回{}条记录", result.size());
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("Top发件人分布调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试获取风险评估
     */
    @GetMapping("/risk-assessment")
    public Map<String, Object> testGetRiskAssessment() {
        log.info("测试调用风险评估接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.getRiskAssessment();
            log.info("风险评估调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("风险评估调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试生成完整分析报告
     */
    @GetMapping("/full-report")
    public Map<String, Object> testGenerateAnalysisReport() {
        log.info("测试调用生成完整分析报告接口");
        try {
            Map<String, Object> result = emailHistoryAnalysisService.generateAnalysisReport();
            log.info("生成完整分析报告调用成功");
            return wrapResponse(true, "调用成功", result);
        } catch (Exception e) {
            log.error("生成完整分析报告调用失败", e);
            return wrapResponse(false, "调用失败: " + e.getMessage(), null);
        }
    }

    /**
     * 批量测试所有接口
     */
    @GetMapping("/batch-test")
    public Map<String, Object> batchTestAllApis() {
        log.info("开始批量测试所有邮件历史分析接口");
        
        Map<String, Object> testResults = new LinkedHashMap<>();
        
        // 测试基础统计接口
        testResults.put("statistics", testApiCall("statistics", () -> emailHistoryAnalysisService.getEmailStatisticsReport()));
        testResults.put("timeDistribution", testApiCall("timeDistribution", () -> emailHistoryAnalysisService.getEmailTimeDistribution()));
        testResults.put("statusDistribution", testApiCall("statusDistribution", () -> emailHistoryAnalysisService.getEmailStatusDistribution()));
        testResults.put("typeDistribution", testApiCall("typeDistribution", () -> emailHistoryAnalysisService.getEmailTypeDistribution()));
        
        // 测试分析接口
        testResults.put("cleanupImpact3Months", testApiCall("cleanupImpact3Months", () -> emailHistoryAnalysisService.evaluateCleanupImpact(3)));
        testResults.put("cleanupImpact6Months", testApiCall("cleanupImpact6Months", () -> emailHistoryAnalysisService.evaluateCleanupImpact(6)));
        testResults.put("cleanupPlan", testApiCall("cleanupPlan", () -> emailHistoryAnalysisService.generateCleanupPlan()));
        
        // 测试分布接口
        testResults.put("topUsers", testApiCall("topUsers", () -> emailHistoryAnalysisService.getTopUsersEmailDistribution(5)));
        testResults.put("topSenders", testApiCall("topSenders", () -> emailHistoryAnalysisService.getTopSendersDistribution(5)));
        
        // 测试风险评估
        testResults.put("riskAssessment", testApiCall("riskAssessment", () -> emailHistoryAnalysisService.getRiskAssessment()));
        
        log.info("批量测试完成");
        return wrapResponse(true, "批量测试完成", testResults);
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> health = new LinkedHashMap<>();
        health.put("service", "EmailHistoryAnalysisTestController");
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        
        // 测试一个简单的接口调用
        try {
            Map<String, Object> stats = emailHistoryAnalysisService.getEmailStatisticsReport();
            health.put("feignClientStatus", "UP");
            health.put("totalEmails", stats.get("totalEmails"));
        } catch (Exception e) {
            health.put("feignClientStatus", "DOWN");
            health.put("error", e.getMessage());
        }
        
        return health;
    }

    // ========== 私有辅助方法 ==========
    
    private Map<String, Object> wrapResponse(boolean success, String message, Object data) {
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", success);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }
    
    private Map<String, Object> testApiCall(String apiName, ApiCallable callable) {
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("apiName", apiName);
        
        try {
            long startTime = System.currentTimeMillis();
            Object data = callable.call();
            long endTime = System.currentTimeMillis();
            
            result.put("success", true);
            result.put("responseTime", endTime - startTime + "ms");
            result.put("hasData", data != null);
            
            if (data instanceof Map) {
                result.put("dataSize", ((Map<?, ?>) data).size());
            } else if (data instanceof List) {
                result.put("dataSize", ((List<?>) data).size());
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("API调用失败: {}", apiName, e);
        }
        
        return result;
    }
    
    @FunctionalInterface
    private interface ApiCallable {
        Object call() throws Exception;
    }
}
