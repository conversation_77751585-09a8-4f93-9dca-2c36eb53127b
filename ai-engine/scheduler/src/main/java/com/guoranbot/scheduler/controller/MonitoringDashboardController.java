package com.guoranbot.scheduler.controller;

import com.guoranbot.scheduler.service.EmailCleanupMonitoringService;
import com.guoranbot.scheduler.service.NotificationService;
import com.guoranbot.scheduler.config.EmailCleanupMonitoringConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 监控仪表板控制器
 * 提供监控数据的Web界面展示
 */
@Slf4j
@Controller
@RequestMapping("/monitoring")
public class MonitoringDashboardController {

    @Autowired
    private EmailCleanupMonitoringService monitoringService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private EmailCleanupMonitoringConfig monitoringConfig;

    /**
     * 监控仪表板首页
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        try {
            // 获取监控数据
            Map<String, Object> dashboardData = monitoringService.getMonitoringDashboard();
            model.addAttribute("dashboardData", dashboardData);
            
            // 获取配置信息
            model.addAttribute("config", monitoringConfig);
            
            // 获取通知统计
            Map<String, Object> notificationStats = notificationService.getNotificationStatistics();
            model.addAttribute("notificationStats", notificationStats);
            
            return "monitoring/dashboard";
        } catch (Exception e) {
            log.error("获取监控仪表板数据失败", e);
            model.addAttribute("error", "获取监控数据失败: " + e.getMessage());
            return "monitoring/error";
        }
    }

    /**
     * 获取实时监控数据 (AJAX接口)
     */
    @GetMapping("/api/realtime")
    @ResponseBody
    public Map<String, Object> getRealtimeData() {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("metrics", monitoringService.getRealtimeMetrics());
            data.put("alerts", monitoringService.checkAlertConditions());
            data.put("notifications", notificationService.getNotificationStatistics());
            data.put("timestamp", System.currentTimeMillis());
            return data;
        } catch (Exception e) {
            log.error("获取实时监控数据失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            return errorData;
        }
    }

    /**
     * 获取历史统计数据
     */
    @GetMapping("/api/statistics")
    @ResponseBody
    public Map<String, Object> getStatistics(@RequestParam(defaultValue = "24") int hours) {
        try {
            return monitoringService.getCleanupStatistics(hours);
        } catch (Exception e) {
            log.error("获取历史统计数据失败", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            errorData.put("timestamp", System.currentTimeMillis());
            return errorData;
        }
    }

    /**
     * 测试通知服务
     */
    @PostMapping("/api/test-notification")
    @ResponseBody
    public Map<String, Object> testNotification(@RequestParam String channel, 
                                               @RequestParam(defaultValue = "监控系统测试") String message) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = notificationService.testNotification(channel, message);
            result.put("success", success);
            result.put("message", success ? "测试通知发送成功" : "测试通知发送失败");
            result.put("channel", channel);
            result.put("timestamp", System.currentTimeMillis());
        } catch (Exception e) {
            log.error("测试通知失败", e);
            result.put("success", false);
            result.put("message", "测试通知异常: " + e.getMessage());
            result.put("channel", channel);
            result.put("timestamp", System.currentTimeMillis());
        }
        return result;
    }

    /**
     * 手动发送告警
     */
    @PostMapping("/api/send-alert")
    @ResponseBody
    public Map<String, Object> sendAlert(@RequestParam String alertType,
                                        @RequestParam String message,
                                        @RequestParam(defaultValue = "MEDIUM") String severity) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("source", "manual");
            additionalData.put("operator", "admin");
            
            notificationService.sendAlert(alertType, message, severity, additionalData);
            
            result.put("success", true);
            result.put("message", "告警发送成功");
            result.put("alertType", alertType);
            result.put("severity", severity);
            result.put("timestamp", System.currentTimeMillis());
        } catch (Exception e) {
            log.error("手动发送告警失败", e);
            result.put("success", false);
            result.put("message", "告警发送失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        return result;
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/api/health")
    @ResponseBody
    public Map<String, Object> getSystemHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 获取监控指标
            Map<String, Object> metrics = monitoringService.getRealtimeMetrics();
            double successRate = (Double) metrics.getOrDefault("successRate", 0.0);
            int activeTasks = (Integer) metrics.getOrDefault("currentActiveTasks", 0);
            
            // 检查告警条件
            Map<String, Object> alerts = monitoringService.checkAlertConditions();
            int activeAlertCount = (Integer) alerts.getOrDefault("totalAlerts", 0);
            
            // 检查通知服务状态
            boolean dingTalkAvailable = notificationService.isChannelAvailable("dingtalk");
            boolean emailAvailable = notificationService.isChannelAvailable("email");
            boolean smsAvailable = notificationService.isChannelAvailable("sms");
            
            // 计算整体健康状态
            String status;
            String message;
            
            if (successRate >= 0.95 && activeTasks <= monitoringConfig.getAlerts().getMaxActiveTasks() 
                && activeAlertCount == 0 && dingTalkAvailable) {
                status = "HEALTHY";
                message = "系统运行正常";
            } else if (successRate >= 0.9 && activeTasks <= monitoringConfig.getAlerts().getMaxActiveTasks() * 2) {
                status = "WARNING";
                message = "系统存在告警，需要关注";
            } else {
                status = "CRITICAL";
                message = "系统状态异常，需要立即处理";
            }
            
            health.put("status", status);
            health.put("message", message);
            health.put("successRate", successRate);
            health.put("activeTasks", activeTasks);
            health.put("activeAlerts", activeAlertCount);
            health.put("services", Map.of(
                "dingtalk", dingTalkAvailable,
                "email", emailAvailable,
                "sms", smsAvailable
            ));
            health.put("timestamp", System.currentTimeMillis());
            
            return health;
            
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return Map.of(
                "status", "ERROR",
                "message", "健康检查异常: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );
        }
    }

    /**
     * 获取配置信息
     */
    @GetMapping("/api/config")
    @ResponseBody
    public Map<String, Object> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", monitoringConfig.isEnabled());
        config.put("alerts", monitoringConfig.getAlerts());
        config.put("retention", monitoringConfig.getRetention());
        config.put("notification", monitoringConfig.getNotification());
        config.put("summary", monitoringConfig.getConfigSummary());
        config.put("valid", monitoringConfig.isValid());
        return config;
    }

    /**
     * 错误页面
     */
    @GetMapping("/error")
    public String error() {
        return "monitoring/error";
    }
}
