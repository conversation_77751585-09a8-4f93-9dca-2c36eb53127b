package com.guoranbot.scheduler.controller;

import com.guoranbot.scheduler.service.BeanConflictDiagnosticService;
import com.guoranbot.scheduler.service.EmailCleanupDiagnosticService;
import com.guoranbot.scheduler.service.NacosServiceDiscoveryChecker;
import com.guoranbot.scheduler.service.ServiceHealthChecker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务健康检查控制器
 */
@Slf4j
@Api(tags = "服务健康检查", description = "提供各个依赖服务的健康状态检查")
@RestController
@RequestMapping("/health")
public class ServiceHealthController {

    @Autowired
    private ServiceHealthChecker serviceHealthChecker;

    @Autowired
    private EmailCleanupDiagnosticService diagnosticService;

    @Autowired
    private NacosServiceDiscoveryChecker nacosChecker;

    @Autowired
    private BeanConflictDiagnosticService beanConflictDiagnosticService;

    /**
     * 检查Data Service健康状态
     */
    @ApiOperation(value = "检查Data Service健康状态", notes = "检查Data Service是否可用")
    @GetMapping("/data-service")
    public ResponseEntity<Boolean> checkDataServiceHealth() {
        boolean isHealthy = serviceHealthChecker.isDataServiceHealthy();
        log.info("Data service health check result: {}", isHealthy);
        return ResponseEntity.ok(isHealthy);
    }

    /**
     * 检查AIFAPIAO邮箱账户接口健康状态
     */
    @ApiOperation(value = "检查AIFAPIAO邮箱账户接口健康状态", notes = "检查getAifapiaoEmailAccounts接口是否可用")
    @GetMapping("/aifapiao-accounts")
    public ResponseEntity<Boolean> checkAifapiaoAccountsEndpointHealth() {
        boolean isHealthy = serviceHealthChecker.isAifapiaoAccountsEndpointHealthy();
        log.info("AIFAPIAO accounts endpoint health check result: {}", isHealthy);
        return ResponseEntity.ok(isHealthy);
    }

    /**
     * 获取完整的健康状态报告
     */
    @ApiOperation(value = "获取完整的健康状态报告", notes = "获取所有依赖服务的健康状态")
    @GetMapping("/report")
    public ResponseEntity<ServiceHealthChecker.HealthReport> getHealthReport() {
        ServiceHealthChecker.HealthReport report = serviceHealthChecker.getHealthReport();
        log.info("Health report generated: {}", report);
        return ResponseEntity.ok(report);
    }

    /**
     * 检查整体健康状态
     */
    @ApiOperation(value = "检查整体健康状态", notes = "检查所有依赖服务是否都健康")
    @GetMapping("/overall")
    public ResponseEntity<Boolean> checkOverallHealth() {
        ServiceHealthChecker.HealthReport report = serviceHealthChecker.getHealthReport();
        boolean isHealthy = report.isOverallHealthy();
        log.info("Overall health check result: {}", isHealthy);
        return ResponseEntity.ok(isHealthy);
    }

    /**
     * 检查Nacos服务发现状态
     */
    @ApiOperation(value = "检查Nacos服务发现状态", notes = "检查data-service在Nacos中的注册状态和实例健康情况")
    @GetMapping("/nacos")
    public ResponseEntity<NacosServiceDiscoveryChecker.ServiceDiscoveryReport> checkNacosServiceDiscovery() {
        log.info("Checking Nacos service discovery for data-service");
        NacosServiceDiscoveryChecker.ServiceDiscoveryReport report = nacosChecker.checkDataServiceRegistration();
        return ResponseEntity.ok(report);
    }

    /**
     * 检查Bean冲突问题
     */
    @ApiOperation(value = "检查Bean冲突问题", notes = "诊断Spring容器中的Bean名称冲突问题")
    @GetMapping("/bean-conflicts")
    public ResponseEntity<BeanConflictDiagnosticService.BeanConflictReport> checkBeanConflicts() {
        log.info("Checking for bean conflicts");
        BeanConflictDiagnosticService.BeanConflictReport report = beanConflictDiagnosticService.diagnoseBeanConflicts();
        return ResponseEntity.ok(report);
    }

    /**
     * 运行完整的诊断检查
     */
    @ApiOperation(value = "运行完整的诊断检查", notes = "执行全面的邮件清理服务诊断，包括Nacos检查、Bean冲突检查、问题分析和修复建议")
    @GetMapping("/diagnostic")
    public ResponseEntity<EmailCleanupDiagnosticService.DiagnosticReport> runDiagnostic() {
        log.info("Running full diagnostic check for email cleanup service");
        EmailCleanupDiagnosticService.DiagnosticReport report = diagnosticService.runFullDiagnostic();
        return ResponseEntity.ok(report);
    }
}
