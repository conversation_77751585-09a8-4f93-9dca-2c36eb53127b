package com.guoranbot.scheduler.service;

import com.guoranbot.scheduler.config.FeignConfigurationManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Bean冲突诊断服务
 */
@Slf4j
@Service
public class BeanConflictDiagnosticService {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 诊断Bean冲突问题
     */
    public BeanConflictReport diagnoseBeanConflicts() {
        BeanConflictReport report = new BeanConflictReport();

        try {
            // 记录Bean配置信息
            FeignConfigurationManager.BeanConflictInfo.logBeanInfo();

            // 检查常见的冲突Bean
            checkFeignOptionsBeans(report);
            checkRestTemplateBeans(report);
            checkRetryerBeans(report);
            checkErrorDecoderBeans(report);
            checkFeignClientBeans(report);

        } catch (Exception e) {
            log.error("Error diagnosing bean conflicts", e);
            report.addIssue("Error during diagnosis: " + e.getMessage());
        }

        return report;
    }

    /**
     * 检查FeignOptions相关Bean
     */
    private void checkFeignOptionsBeans(BeanConflictReport report) {
        try {
            String[] beanNames = applicationContext.getBeanNamesForType(feign.Request.Options.class);
            report.addBeanInfo("Request.Options", Arrays.asList(beanNames));
            
            if (beanNames.length > 1) {
                report.addIssue("Multiple Request.Options beans found: " + Arrays.toString(beanNames));
                report.addRecommendation("Remove @Configuration from Feign config classes to make them non-global");
                report.addRecommendation("Use @ConditionalOnMissingBean for global default beans");
            }
            
        } catch (Exception e) {
            report.addIssue("Error checking Request.Options beans: " + e.getMessage());
        }
    }

    /**
     * 检查RestTemplate相关Bean
     */
    private void checkRestTemplateBeans(BeanConflictReport report) {
        try {
            String[] beanNames = applicationContext.getBeanNamesForType(org.springframework.web.client.RestTemplate.class);
            report.addBeanInfo("RestTemplate", Arrays.asList(beanNames));
            
            if (beanNames.length > 1) {
                report.addIssue("Multiple RestTemplate beans found: " + Arrays.toString(beanNames));
                report.addRecommendation("Use @Primary annotation or unique bean names");
            }
            
        } catch (Exception e) {
            report.addIssue("Error checking RestTemplate beans: " + e.getMessage());
        }
    }

    /**
     * 检查Retryer相关Bean
     */
    private void checkRetryerBeans(BeanConflictReport report) {
        try {
            String[] beanNames = applicationContext.getBeanNamesForType(feign.Retryer.class);
            report.addBeanInfo("Retryer", Arrays.asList(beanNames));
            
            if (beanNames.length > 1) {
                report.addIssue("Multiple Retryer beans found: " + Arrays.toString(beanNames));
                report.addRecommendation("Remove @Configuration from Feign config classes to make them non-global");
                report.addRecommendation("Each Feign client should use its own configuration class");
                report.addRecommendation("Use @ConditionalOnMissingBean for global default beans");
            }
            
        } catch (Exception e) {
            report.addIssue("Error checking Retryer beans: " + e.getMessage());
        }
    }

    /**
     * 检查ErrorDecoder相关Bean
     */
    private void checkErrorDecoderBeans(BeanConflictReport report) {
        try {
            String[] beanNames = applicationContext.getBeanNamesForType(feign.codec.ErrorDecoder.class);
            report.addBeanInfo("ErrorDecoder", Arrays.asList(beanNames));

            if (beanNames.length > 1) {
                report.addIssue("Multiple ErrorDecoder beans found: " + Arrays.toString(beanNames));
                report.addRecommendation("Use unique bean names like @Bean(\"dataServiceErrorDecoder\")");
            }

        } catch (Exception e) {
            report.addIssue("Error checking ErrorDecoder beans: " + e.getMessage());
        }
    }

    /**
     * 检查FeignClient相关Bean
     */
    private void checkFeignClientBeans(BeanConflictReport report) {
        try {
            // 检查特定的Feign客户端
            String[] dataServiceBeans = applicationContext.getBeanNamesForType(
                com.guoranbot.scheduler.service.feign.IDataInfoService.class);
            String[] emailHistoryBeans = applicationContext.getBeanNamesForType(
                com.guoranbot.scheduler.service.feign.IEmailHistoryAnalysisService.class);

            report.addBeanInfo("IDataInfoService", Arrays.asList(dataServiceBeans));
            report.addBeanInfo("IEmailHistoryAnalysisService", Arrays.asList(emailHistoryBeans));

            if (dataServiceBeans.length == 0) {
                report.addIssue("No IDataInfoService bean found");
            }

            if (emailHistoryBeans.length == 0) {
                report.addIssue("No IEmailHistoryAnalysisService bean found");
            }

        } catch (Exception e) {
            report.addIssue("Error checking FeignClient beans: " + e.getMessage());
        }
    }

    /**
     * Bean冲突诊断报告
     */
    public static class BeanConflictReport {
        private Map<String, Object> beanInfo = new HashMap<>();
        private Map<String, Object> issues = new HashMap<>();
        private Map<String, Object> recommendations = new HashMap<>();

        public Map<String, Object> getBeanInfo() { return beanInfo; }
        public Map<String, Object> getIssues() { return issues; }
        public Map<String, Object> getRecommendations() { return recommendations; }

        public void addBeanInfo(String type, Object info) {
            beanInfo.put(type, info);
        }

        public void addIssue(String issue) {
            issues.put("issue_" + (issues.size() + 1), issue);
        }

        public void addRecommendation(String recommendation) {
            recommendations.put("rec_" + (recommendations.size() + 1), recommendation);
        }

        @Override
        public String toString() {
            return String.format("BeanConflictReport{beans=%d, issues=%d, recommendations=%d}", 
                               beanInfo.size(), issues.size(), recommendations.size());
        }
    }
}
