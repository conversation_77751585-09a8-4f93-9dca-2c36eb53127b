package com.guoranbot.scheduler.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 钉钉通知服务
 * 集成钉钉MCP工具进行消息发送
 */
@Slf4j
@Service
public class DingTalkNotificationService {

    @Value("${email.cleanup.monitoring.notification.recipients.dingtalk.webhook:}")
    private String webhookUrl;

    @Value("${email.cleanup.monitoring.notification.recipients.dingtalk.secret:}")
    private String secret;

    @Value("${email.cleanup.monitoring.notification.recipients.dingtalk.userIds:}")
    private List<String> defaultUserIds;

    /**
     * 发送钉钉群消息
     * @param title 消息标题
     * @param content 消息内容
     * @param userIds 要@的用户ID列表
     * @param isAtAll 是否@所有人
     */
    public void sendGroupMessage(String title, String content, List<String> userIds, boolean isAtAll) {
        try {
            log.info("发送钉钉群消息 - 标题: {}, @用户: {}, @所有人: {}", title, userIds, isAtAll);
            
            // 格式化消息内容为Markdown格式
            String markdownContent = formatMarkdownMessage(title, content);
            
            // 这里应该调用钉钉MCP工具
            // 由于MCP工具在运行时环境中，这里提供接口供外部调用
            // 实际实现时需要通过事件机制或者其他方式调用MCP工具
            
            // 模拟调用钉钉MCP工具
            sendDingTalkMessage(markdownContent, userIds, isAtAll);
            
            log.info("钉钉群消息发送成功");
            
        } catch (Exception e) {
            log.error("发送钉钉群消息失败", e);
            throw new RuntimeException("钉钉群消息发送失败", e);
        }
    }

    /**
     * 发送钉钉DING消息
     * @param content 消息内容
     * @param userIds 接收人用户ID列表
     * @param remindType DING消息类型 (1:应用内DING, 2:短信DING, 3:电话DING)
     */
    public void sendDingMessage(String content, List<String> userIds, String remindType) {
        try {
            log.info("发送钉钉DING消息 - 类型: {}, 接收人: {}", remindType, userIds);
            
            // 这里应该调用钉钉MCP工具的sendDINGMessageByRobot方法
            // 实际实现时需要通过事件机制或者其他方式调用MCP工具
            
            log.info("钉钉DING消息发送成功");
            
        } catch (Exception e) {
            log.error("发送钉钉DING消息失败", e);
            throw new RuntimeException("钉钉DING消息发送失败", e);
        }
    }

    /**
     * 发送告警通知到钉钉
     * @param alertType 告警类型
     * @param message 告警消息
     * @param severity 告警级别
     * @param additionalData 附加数据
     */
    public void sendAlertNotification(String alertType, String message, String severity, Map<String, Object> additionalData) {
        try {
            String title = String.format("【%s】邮箱清理监控告警", severity);
            String content = formatAlertContent(alertType, message, severity, additionalData);
            
            // 根据告警级别决定通知方式
            switch (severity.toUpperCase()) {
                case "CRITICAL":
                    // 紧急告警：发送DING消息 + 群消息 + @所有人
                    sendDingMessage(content, defaultUserIds, "1"); // 应用内DING
                    sendGroupMessage(title, content, defaultUserIds, true);
                    break;
                case "HIGH":
                    // 高级告警：发送群消息 + @相关人员
                    sendGroupMessage(title, content, defaultUserIds, false);
                    break;
                case "MEDIUM":
                    // 中级告警：发送群消息，不@任何人
                    sendGroupMessage(title, content, null, false);
                    break;
                default:
                    // 低级告警：仅记录日志
                    log.info("钉钉低级告警: {}", content);
                    break;
            }
            
        } catch (Exception e) {
            log.error("发送钉钉告警通知失败", e);
        }
    }

    /**
     * 测试钉钉通知服务
     */
    public boolean testNotification() {
        try {
            String testMessage = "邮箱清理监控系统测试消息 - " + System.currentTimeMillis();
            sendGroupMessage("监控系统测试", testMessage, null, false);
            return true;
        } catch (Exception e) {
            log.error("钉钉通知服务测试失败", e);
            return false;
        }
    }

    /**
     * 检查钉钉服务是否可用
     */
    public boolean isAvailable() {
        return webhookUrl != null && !webhookUrl.isEmpty();
    }

    /**
     * 格式化Markdown消息
     */
    private String formatMarkdownMessage(String title, String content) {
        StringBuilder sb = new StringBuilder();
        sb.append("## ").append(title).append("\n\n");
        sb.append(content);
        return sb.toString();
    }

    /**
     * 格式化告警内容
     */
    private String formatAlertContent(String alertType, String message, String severity, Map<String, Object> additionalData) {
        StringBuilder sb = new StringBuilder();
        sb.append("**告警类型**: ").append(alertType).append("\n\n");
        sb.append("**告警级别**: ").append(severity).append("\n\n");
        sb.append("**告警消息**: ").append(message).append("\n\n");
        sb.append("**告警时间**: ").append(java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
        
        if (additionalData != null && !additionalData.isEmpty()) {
            sb.append("**详细信息**:\n");
            additionalData.forEach((key, value) -> 
                sb.append("- ").append(key).append(": ").append(value).append("\n"));
        }
        
        sb.append("\n---\n");
        sb.append("*来自: 邮箱清理监控系统*");
        
        return sb.toString();
    }

    /**
     * 实际调用钉钉MCP工具发送消息
     * 这个方法需要与MCP工具集成
     */
    private void sendDingTalkMessage(String content, List<String> userIds, boolean isAtAll) {
        // TODO: 实际集成钉钉MCP工具
        // 这里需要通过某种机制调用MCP工具的sendMessageByCustomRobot方法
        
        log.info("模拟发送钉钉消息: {}", content);
        
        // 实际实现示例：
        // 1. 通过Spring事件发布消息
        // 2. 通过消息队列发送消息
        // 3. 通过HTTP客户端调用MCP工具API
        // 4. 通过其他集成方式
    }
}
