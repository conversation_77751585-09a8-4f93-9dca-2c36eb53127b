package com.guoranbot.scheduler.service;

import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.scheduler.service.feign.IDataInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件清理诊断服务
 */
@Slf4j
@Service
public class EmailCleanupDiagnosticService {

    @Autowired
    private IDataInfoService dataInfoService;

    @Autowired
    private ServiceHealthChecker serviceHealthChecker;

    @Autowired
    private NacosServiceDiscoveryChecker nacosChecker;

    /**
     * 执行完整的诊断检查
     */
    public DiagnosticReport runFullDiagnostic() {
        log.info("Starting full diagnostic check for email cleanup service");
        
        DiagnosticReport report = new DiagnosticReport();
        
        // 1. 检查服务健康状态
        ServiceHealthChecker.HealthReport healthReport = serviceHealthChecker.getHealthReport();
        report.setHealthReport(healthReport);
        
        // 2. 检查Nacos服务发现
        checkNacosServiceDiscovery(report);

        // 3. 检查AIFAPIAO邮箱账户
        checkAifapiaoAccounts(report);

        // 4. 检查服务连接性
        checkServiceConnectivity(report);

        // 5. 生成诊断建议
        generateRecommendations(report);
        
        log.info("Full diagnostic check completed: {}", report);
        return report;
    }

    /**
     * 检查Nacos服务发现
     */
    private void checkNacosServiceDiscovery(DiagnosticReport report) {
        try {
            log.debug("Checking Nacos service discovery...");
            NacosServiceDiscoveryChecker.ServiceDiscoveryReport nacosReport =
                nacosChecker.checkDataServiceRegistration();

            report.setNacosReport(nacosReport);

            if (!nacosReport.isDataServiceRegistered()) {
                report.addIssue("data-service is not registered in Nacos");
            } else if (nacosReport.getDataServiceInstances() == 0) {
                report.addIssue("data-service is registered but has no healthy instances");
            } else {
                report.addInfo(String.format("data-service has %d registered instances in Nacos",
                              nacosReport.getDataServiceInstances()));

                // 检查特定接口
                boolean endpointAvailable = nacosChecker.checkDataServiceEndpoint("/email/cleanup/aifapiao-accounts");
                if (!endpointAvailable) {
                    report.addIssue("AIFAPIAO accounts endpoint is not accessible on any data-service instance");
                } else {
                    report.addInfo("AIFAPIAO accounts endpoint is accessible");
                }
            }

        } catch (Exception e) {
            report.addIssue("Error checking Nacos service discovery: " + e.getMessage());
        }
    }

    /**
     * 检查AIFAPIAO邮箱账户
     */
    private void checkAifapiaoAccounts(DiagnosticReport report) {
        try {
            log.debug("Checking AIFAPIAO email accounts...");
            List<AccountUserEmail> accounts = dataInfoService.getAifapiaoEmailAccounts();
            
            if (accounts == null) {
                report.addIssue("AIFAPIAO accounts API returned null");
                report.setAifapiaoAccountsCount(0);
            } else {
                report.setAifapiaoAccountsCount(accounts.size());
                if (accounts.isEmpty()) {
                    report.addIssue("No AIFAPIAO email accounts found");
                } else {
                    log.info("Found {} AIFAPIAO email accounts", accounts.size());
                    // 记录前几个账户用于调试（不记录密码）
                    for (int i = 0; i < Math.min(3, accounts.size()); i++) {
                        report.addInfo(String.format("Sample account %d: %s", i + 1, accounts.get(i).getEmail()));
                    }
                }
            }
            
        } catch (feign.FeignException.NotFound e) {
            report.addIssue("AIFAPIAO accounts endpoint not found (404): " + e.getMessage());
        } catch (feign.FeignException e) {
            report.addIssue(String.format("Feign error accessing AIFAPIAO accounts: status=%d, message=%s", 
                           e.status(), e.getMessage()));
        } catch (Exception e) {
            report.addIssue("Unexpected error accessing AIFAPIAO accounts: " + e.getMessage());
        }
    }

    /**
     * 检查服务连接性
     */
    private void checkServiceConnectivity(DiagnosticReport report) {
        try {
            log.debug("Checking service connectivity...");
            Integer syncStatus = dataInfoService.checkSyncStatus();
            report.addInfo("Data service connectivity check passed, sync status: " + syncStatus);
        } catch (Exception e) {
            report.addIssue("Data service connectivity check failed: " + e.getMessage());
        }
    }

    /**
     * 生成诊断建议
     */
    private void generateRecommendations(DiagnosticReport report) {
        // Nacos相关建议
        if (report.getNacosReport() != null && !report.getNacosReport().isDataServiceRegistered()) {
            report.addRecommendation("Check if data-service is running and properly configured for Nacos registration");
            report.addRecommendation("Verify Nacos server address and namespace configuration in data-service");
            report.addRecommendation("Check data-service logs for Nacos registration errors");
        }

        if (!report.getHealthReport().isDataServiceHealthy()) {
            report.addRecommendation("Check if data-service is running and registered in Nacos");
            report.addRecommendation("Verify network connectivity between scheduler-service and data-service");
            report.addRecommendation("Ensure both services are in the same Nacos namespace and group");
        }
        
        if (!report.getHealthReport().isAifapiaoAccountsEndpointHealthy()) {
            report.addRecommendation("Verify that /email/cleanup/aifapiao-accounts endpoint exists in data-service");
            report.addRecommendation("Check data-service logs for any errors");
        }
        
        if (report.getAifapiaoAccountsCount() == 0) {
            report.addRecommendation("Check if there are any users with AIFAPIAO email accounts in the database");
            report.addRecommendation("Verify the AccountUserService.getAifapiaoEmailAccounts() implementation");
        }
        
        if (report.getIssues().isEmpty()) {
            report.addRecommendation("All checks passed - the email cleanup service should be working correctly");
        }
    }

    /**
     * 诊断报告
     */
    public static class DiagnosticReport {
        private ServiceHealthChecker.HealthReport healthReport;
        private NacosServiceDiscoveryChecker.ServiceDiscoveryReport nacosReport;
        private int aifapiaoAccountsCount;
        private Map<String, Object> issues = new HashMap<>();
        private Map<String, Object> info = new HashMap<>();
        private Map<String, Object> recommendations = new HashMap<>();

        public ServiceHealthChecker.HealthReport getHealthReport() {
            return healthReport;
        }

        public void setHealthReport(ServiceHealthChecker.HealthReport healthReport) {
            this.healthReport = healthReport;
        }

        public NacosServiceDiscoveryChecker.ServiceDiscoveryReport getNacosReport() {
            return nacosReport;
        }

        public void setNacosReport(NacosServiceDiscoveryChecker.ServiceDiscoveryReport nacosReport) {
            this.nacosReport = nacosReport;
        }

        public int getAifapiaoAccountsCount() {
            return aifapiaoAccountsCount;
        }

        public void setAifapiaoAccountsCount(int aifapiaoAccountsCount) {
            this.aifapiaoAccountsCount = aifapiaoAccountsCount;
        }

        public Map<String, Object> getIssues() {
            return issues;
        }

        public Map<String, Object> getInfo() {
            return info;
        }

        public Map<String, Object> getRecommendations() {
            return recommendations;
        }

        public void addIssue(String issue) {
            issues.put("issue_" + (issues.size() + 1), issue);
        }

        public void addInfo(String info) {
            this.info.put("info_" + (this.info.size() + 1), info);
        }

        public void addRecommendation(String recommendation) {
            recommendations.put("rec_" + (recommendations.size() + 1), recommendation);
        }

        @Override
        public String toString() {
            return String.format("DiagnosticReport{health=%s, accounts=%d, issues=%d, recommendations=%d}", 
                               healthReport != null ? healthReport.isOverallHealthy() : "unknown", 
                               aifapiaoAccountsCount, issues.size(), recommendations.size());
        }
    }
}
