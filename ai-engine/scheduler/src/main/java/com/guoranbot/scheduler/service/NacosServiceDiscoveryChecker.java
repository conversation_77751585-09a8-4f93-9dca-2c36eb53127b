package com.guoranbot.scheduler.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Nacos服务发现检查器
 */
@Slf4j
@Service
public class NacosServiceDiscoveryChecker {

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 检查data-service在Nacos中的注册状态
     */
    public ServiceDiscoveryReport checkDataServiceRegistration() {
        ServiceDiscoveryReport report = new ServiceDiscoveryReport();
        
        try {
            // 获取所有注册的服务
            List<String> services = discoveryClient.getServices();
            log.info("Found {} services in Nacos: {}", services.size(), services);
            
            report.setTotalServices(services.size());
            report.setAllServices(services);
            
            // 检查data-service是否注册
            boolean dataServiceRegistered = services.contains("data-service");
            report.setDataServiceRegistered(dataServiceRegistered);
            
            if (dataServiceRegistered) {
                // 获取data-service的实例信息
                List<ServiceInstance> instances = discoveryClient.getInstances("data-service");
                report.setDataServiceInstances(instances.size());
                
                log.info("Found {} instances of data-service:", instances.size());
                for (ServiceInstance instance : instances) {
                    log.info("  Instance: {}:{} ({})", instance.getHost(), instance.getPort(), instance.getInstanceId());
                    report.addInstanceInfo(String.format("%s:%d", instance.getHost(), instance.getPort()));
                    
                    // 检查实例健康状态
                    checkInstanceHealth(instance, report);
                }
            } else {
                log.warn("data-service is not registered in Nacos!");
                report.addIssue("data-service is not registered in Nacos");
            }
            
        } catch (Exception e) {
            log.error("Error checking service registration in Nacos", e);
            report.addIssue("Error accessing Nacos: " + e.getMessage());
        }
        
        return report;
    }

    /**
     * 检查特定实例的健康状态
     */
    private void checkInstanceHealth(ServiceInstance instance, ServiceDiscoveryReport report) {
        try {
            String healthUrl = String.format("http://%s:%d/actuator/health", 
                                           instance.getHost(), instance.getPort());
            
            // 尝试访问健康检查端点
            Map<String, Object> healthResponse = restTemplate.getForObject(healthUrl, Map.class);
            
            if (healthResponse != null && "UP".equals(healthResponse.get("status"))) {
                report.addHealthyInstance(instance.getInstanceId());
                log.info("Instance {} is healthy", instance.getInstanceId());
            } else {
                report.addUnhealthyInstance(instance.getInstanceId());
                log.warn("Instance {} is not healthy: {}", instance.getInstanceId(), healthResponse);
            }
            
        } catch (Exception e) {
            report.addUnhealthyInstance(instance.getInstanceId());
            log.warn("Cannot check health for instance {}: {}", instance.getInstanceId(), e.getMessage());
        }
    }

    /**
     * 检查特定接口的可用性
     */
    public boolean checkDataServiceEndpoint(String endpoint) {
        try {
            List<ServiceInstance> instances = discoveryClient.getInstances("data-service");
            
            if (instances.isEmpty()) {
                log.warn("No data-service instances available");
                return false;
            }
            
            for (ServiceInstance instance : instances) {
                try {
                    String url = String.format("http://%s:%d%s", 
                                             instance.getHost(), instance.getPort(), endpoint);
                    
                    // 尝试访问接口
                    restTemplate.getForObject(url, Object.class);
                    log.info("Endpoint {} is accessible on instance {}", endpoint, instance.getInstanceId());
                    return true;
                    
                } catch (Exception e) {
                    log.warn("Endpoint {} not accessible on instance {}: {}", 
                            endpoint, instance.getInstanceId(), e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("Error checking endpoint {}", endpoint, e);
        }
        
        return false;
    }

    /**
     * 服务发现报告
     */
    public static class ServiceDiscoveryReport {
        private int totalServices;
        private List<String> allServices;
        private boolean dataServiceRegistered;
        private int dataServiceInstances;
        private Map<String, Object> instanceInfo = new HashMap<>();
        private Map<String, Object> healthyInstances = new HashMap<>();
        private Map<String, Object> unhealthyInstances = new HashMap<>();
        private Map<String, Object> issues = new HashMap<>();

        // Getters and Setters
        public int getTotalServices() { return totalServices; }
        public void setTotalServices(int totalServices) { this.totalServices = totalServices; }

        public List<String> getAllServices() { return allServices; }
        public void setAllServices(List<String> allServices) { this.allServices = allServices; }

        public boolean isDataServiceRegistered() { return dataServiceRegistered; }
        public void setDataServiceRegistered(boolean dataServiceRegistered) { this.dataServiceRegistered = dataServiceRegistered; }

        public int getDataServiceInstances() { return dataServiceInstances; }
        public void setDataServiceInstances(int dataServiceInstances) { this.dataServiceInstances = dataServiceInstances; }

        public Map<String, Object> getInstanceInfo() { return instanceInfo; }
        public Map<String, Object> getHealthyInstances() { return healthyInstances; }
        public Map<String, Object> getUnhealthyInstances() { return unhealthyInstances; }
        public Map<String, Object> getIssues() { return issues; }

        public void addInstanceInfo(String info) {
            instanceInfo.put("instance_" + (instanceInfo.size() + 1), info);
        }

        public void addHealthyInstance(String instanceId) {
            healthyInstances.put("healthy_" + (healthyInstances.size() + 1), instanceId);
        }

        public void addUnhealthyInstance(String instanceId) {
            unhealthyInstances.put("unhealthy_" + (unhealthyInstances.size() + 1), instanceId);
        }

        public void addIssue(String issue) {
            issues.put("issue_" + (issues.size() + 1), issue);
        }

        @Override
        public String toString() {
            return String.format("ServiceDiscoveryReport{totalServices=%d, dataServiceRegistered=%s, instances=%d, issues=%d}", 
                               totalServices, dataServiceRegistered, dataServiceInstances, issues.size());
        }
    }
}
