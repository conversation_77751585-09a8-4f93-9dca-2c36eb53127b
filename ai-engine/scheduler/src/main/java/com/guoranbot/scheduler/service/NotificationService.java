package com.guoranbot.scheduler.service;

import java.util.List;
import java.util.Map;

/**
 * 统一通知服务接口
 * 支持多种通知方式：钉钉、邮件、短信
 */
public interface NotificationService {

    /**
     * 发送告警通知
     * @param alertType 告警类型
     * @param message 告警消息
     * @param severity 告警级别 (CRITICAL, HIGH, MEDIUM, LOW)
     * @param additionalData 附加数据
     */
    void sendAlert(String alertType, String message, String severity, Map<String, Object> additionalData);

    /**
     * 发送钉钉通知
     * @param message 消息内容
     * @param userIds 接收人钉钉ID列表
     * @param isAtAll 是否@所有人
     */
    void sendDingTalkNotification(String message, List<String> userIds, boolean isAtAll);

    /**
     * 发送邮件通知
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param recipients 收件人列表
     * @param isHtml 是否HTML格式
     */
    void sendEmailNotification(String subject, String content, List<String> recipients, boolean isHtml);

    /**
     * 发送短信通知
     * @param message 短信内容
     * @param phoneNumbers 手机号列表
     * @param templateCode 短信模板代码
     */
    void sendSmsNotification(String message, List<String> phoneNumbers, String templateCode);

    /**
     * 根据告警级别获取通知渠道
     * @param severity 告警级别
     * @return 通知渠道列表
     */
    List<String> getNotificationChannels(String severity);

    /**
     * 检查通知服务是否可用
     * @param channel 通知渠道 (dingtalk, email, sms)
     * @return 是否可用
     */
    boolean isChannelAvailable(String channel);

    /**
     * 获取通知发送统计
     * @return 统计信息
     */
    Map<String, Object> getNotificationStatistics();

    /**
     * 测试通知服务
     * @param channel 通知渠道
     * @param testMessage 测试消息
     * @return 测试结果
     */
    boolean testNotification(String channel, String testMessage);
}
