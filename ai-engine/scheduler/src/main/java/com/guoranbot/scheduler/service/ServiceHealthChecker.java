package com.guoranbot.scheduler.service;

import com.guoranbot.scheduler.service.feign.IDataInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 服务健康检查器
 */
@Slf4j
@Service
public class ServiceHealthChecker {

    @Autowired
    private IDataInfoService dataInfoService;

    /**
     * 检查Data Service是否可用
     */
    public boolean isDataServiceHealthy() {
        try {
            log.debug("Checking data service health...");
            
            // 尝试调用一个简单的接口来检查服务状态
            dataInfoService.checkSyncStatus();
            
            log.debug("Data service health check passed");
            return true;
            
        } catch (feign.FeignException.NotFound e) {
            log.warn("Data service endpoint not found: {}", e.getMessage());
            return false;
        } catch (feign.FeignException e) {
            log.warn("Data service health check failed with Feign error: status={}, message={}", 
                    e.status(), e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("Data service health check failed with unexpected error", e);
            return false;
        }
    }

    /**
     * 检查AIFAPIAO邮箱账户接口是否可用
     */
    public boolean isAifapiaoAccountsEndpointHealthy() {
        try {
            log.debug("Checking AIFAPIAO accounts endpoint health...");
            
            // 尝试调用getAifapiaoEmailAccounts接口
            dataInfoService.getAifapiaoEmailAccounts();
            
            log.debug("AIFAPIAO accounts endpoint health check passed");
            return true;
            
        } catch (feign.FeignException.NotFound e) {
            log.error("AIFAPIAO accounts endpoint not found: {}", e.getMessage());
            log.error("Expected endpoint: /email/cleanup/aifapiao-accounts");
            return false;
        } catch (feign.FeignException e) {
            log.warn("AIFAPIAO accounts endpoint health check failed: status={}, message={}", 
                    e.status(), e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("AIFAPIAO accounts endpoint health check failed with unexpected error", e);
            return false;
        }
    }

    /**
     * 获取服务健康状态报告
     */
    public HealthReport getHealthReport() {
        HealthReport report = new HealthReport();
        
        report.setDataServiceHealthy(isDataServiceHealthy());
        report.setAifapiaoAccountsEndpointHealthy(isAifapiaoAccountsEndpointHealthy());
        report.setOverallHealthy(report.isDataServiceHealthy() && report.isAifapiaoAccountsEndpointHealthy());
        
        log.info("Service health report: dataService={}, aifapiaoEndpoint={}, overall={}", 
                report.isDataServiceHealthy(), 
                report.isAifapiaoAccountsEndpointHealthy(), 
                report.isOverallHealthy());
        
        return report;
    }

    /**
     * 健康状态报告
     */
    public static class HealthReport {
        private boolean dataServiceHealthy;
        private boolean aifapiaoAccountsEndpointHealthy;
        private boolean overallHealthy;

        public boolean isDataServiceHealthy() {
            return dataServiceHealthy;
        }

        public void setDataServiceHealthy(boolean dataServiceHealthy) {
            this.dataServiceHealthy = dataServiceHealthy;
        }

        public boolean isAifapiaoAccountsEndpointHealthy() {
            return aifapiaoAccountsEndpointHealthy;
        }

        public void setAifapiaoAccountsEndpointHealthy(boolean aifapiaoAccountsEndpointHealthy) {
            this.aifapiaoAccountsEndpointHealthy = aifapiaoAccountsEndpointHealthy;
        }

        public boolean isOverallHealthy() {
            return overallHealthy;
        }

        public void setOverallHealthy(boolean overallHealthy) {
            this.overallHealthy = overallHealthy;
        }

        @Override
        public String toString() {
            return String.format("HealthReport{dataService=%s, aifapiaoEndpoint=%s, overall=%s}", 
                               dataServiceHealthy, aifapiaoAccountsEndpointHealthy, overallHealthy);
        }
    }
}
