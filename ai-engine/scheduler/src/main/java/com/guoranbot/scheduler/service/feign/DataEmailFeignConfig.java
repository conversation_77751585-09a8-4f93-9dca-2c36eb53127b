package com.guoranbot.scheduler.service.feign;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Email服务Feign客户端配置
 * 注意：这个配置类只为特定的Email相关Feign客户端提供配置，不是全局配置
 */
public class DataEmailFeignConfig {

    @Bean
    public Request.Options feignOptions() {
        return new Request.Options(5000, 10000); // 连接超时时间和读取超时时间（毫秒）
    }
}