package com.guoranbot.scheduler.service.feign;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Data Service Feign客户端配置
 * 注意：这个配置类只为IDataInfoService提供配置，不是全局配置
 */
@Slf4j
public class DataServiceFeignConfig {

    /**
     * 配置连接和读取超时时间
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public Request.Options feignOptions() {
        return new Request.Options(
            10000, // 连接超时时间 10秒
            30000  // 读取超时时间 30秒
        );
    }

    /**
     * 配置重试策略
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public Retryer feignRetryer() {
        // 重试间隔100ms，最大重试间隔1000ms，最大重试次数3次
        return new Retryer.Default(100, 1000, 3);
    }

    /**
     * 配置日志级别
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 自定义错误解码器
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public ErrorDecoder errorDecoder() {
        return new DataServiceErrorDecoder();
    }

    // RestTemplate由全局配置提供，避免Bean冲突
    // 参见：FeignBeanConflictResolver.globalLoadBalancedRestTemplate()

    /**
     * 自定义错误解码器实现
     */
    public static class DataServiceErrorDecoder implements ErrorDecoder {
        private final ErrorDecoder defaultErrorDecoder = new Default();

        @Override
        public Exception decode(String methodKey, feign.Response response) {
            log.error("Data service call failed: method={}, status={}, reason={}",
                     methodKey, response.status(), response.reason());

            if (response.status() == 404) {
                log.error("Data service endpoint not found: {}. Please check:", methodKey);
                log.error("1. Is data-service registered in Nacos?");
                log.error("2. Is the endpoint /email/cleanup/aifapiao-accounts available?");
                log.error("3. Are both services in the same Nacos namespace?");
                return new DataServiceNotFoundException(
                    String.format("Data service endpoint not found: %s", methodKey));
            }

            if (response.status() >= 500) {
                log.error("Data service internal error: method={}, status={}",
                         methodKey, response.status());
                return new DataServiceInternalException(
                    String.format("Data service internal error: %s", methodKey));
            }

            return defaultErrorDecoder.decode(methodKey, response);
        }
    }

    /**
     * Data Service 404异常
     */
    public static class DataServiceNotFoundException extends RuntimeException {
        public DataServiceNotFoundException(String message) {
            super(message);
        }
    }

    /**
     * Data Service 内部错误异常
     */
    public static class DataServiceInternalException extends RuntimeException {
        public DataServiceInternalException(String message) {
            super(message);
        }
    }
}
