package com.guoranbot.scheduler.service.feign;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 邮件历史分析服务Feign客户端配置
 * 注意：这个配置类只为IEmailHistoryAnalysisService提供配置，不是全局配置
 */
@Slf4j
public class EmailHistoryAnalysisFeignConfig {

    /**
     * 配置连接和读取超时时间
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public Request.Options feignOptions() {
        return new Request.Options(
            10000, // 连接超时时间 10秒
            30000  // 读取超时时间 30秒
        );
    }

    /**
     * 配置重试策略
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public Retryer feignRetryer() {
        // 重试间隔100ms，最大重试间隔1000ms，最大重试次数3次
        return new Retryer.Default(100, 1000, 3);
    }

    /**
     * 配置日志级别
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 自定义错误解码器
     * 只对使用此配置的Feign客户端生效
     */
    @Bean
    public ErrorDecoder errorDecoder() {
        return new EmailHistoryAnalysisErrorDecoder();
    }

    /**
     * 邮件历史分析服务错误解码器
     */
    public static class EmailHistoryAnalysisErrorDecoder implements ErrorDecoder {
        private final ErrorDecoder defaultErrorDecoder = new Default();

        @Override
        public Exception decode(String methodKey, feign.Response response) {
            log.error("Email history analysis service call failed: method={}, status={}, reason={}", 
                     methodKey, response.status(), response.reason());
            
            if (response.status() == 404) {
                log.error("Email history analysis endpoint not found: {}. Please check:", methodKey);
                log.error("1. Is data-service registered in Nacos?");
                log.error("2. Is the endpoint /email/analysis/* available?");
                log.error("3. Are both services in the same Nacos namespace?");
                return new EmailHistoryAnalysisNotFoundException(
                    String.format("Email history analysis endpoint not found: %s", methodKey));
            }
            
            if (response.status() >= 500) {
                log.error("Email history analysis service internal error: method={}, status={}", 
                         methodKey, response.status());
                return new EmailHistoryAnalysisInternalException(
                    String.format("Email history analysis service internal error: %s", methodKey));
            }
            
            return defaultErrorDecoder.decode(methodKey, response);
        }
    }

    /**
     * 邮件历史分析服务404异常
     */
    public static class EmailHistoryAnalysisNotFoundException extends RuntimeException {
        public EmailHistoryAnalysisNotFoundException(String message) {
            super(message);
        }
    }

    /**
     * 邮件历史分析服务内部错误异常
     */
    public static class EmailHistoryAnalysisInternalException extends RuntimeException {
        public EmailHistoryAnalysisInternalException(String message) {
            super(message);
        }
    }
}
