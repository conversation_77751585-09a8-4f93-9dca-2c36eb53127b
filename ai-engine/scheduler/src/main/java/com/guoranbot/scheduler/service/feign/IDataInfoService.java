package com.guoranbot.scheduler.service.feign;

import com.guoranbot.common.dto.*;
import com.guoranbot.common.dto.toc.EmailFilter;
import com.guoranbot.common.po.*;
import com.guoranbot.common.po.toc.TocStatisticsDay;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(name = "data-service", contextId = "dataInfoService",
             configuration = DataServiceFeignConfig.class,
             fallback = IDataInfoServiceFallback.class)
public interface IDataInfoService {

    @RequestMapping("/invoice/page/un-verify")
    PageInfoDto<InvoiceInfo> getUnVerifyInvoice(@RequestParam("pageSize") Integer pageSize,
                                                @RequestParam("pageNo") Integer pageNo);

    @RequestMapping(value = "/invoice/update",method = RequestMethod.POST)
    Integer update(@RequestBody List<InvoiceInfo> invoiceInfos);

    @GetMapping("/invoice/manual-classify")
    List<InvoiceInfo> getManualClassifyInvoice(@RequestParam(value = "startTime",required = false) Long startTime,
                                               @RequestParam(value = "endTime",required = false) Long endTime);
    @GetMapping("/train/record/latest-time")
    Long getLatestTrainTime();

    @PostMapping("/train/record")
    Long save(@RequestBody MlTrainRecord record);

    @RequestMapping("/email/check")
    Integer checkSyncStatus();

    @GetMapping("/email/page/retry-emails")
    PageInfoDto<EmailRecordDto> getRetryEmailRecords(@RequestParam("pageSize") Integer pageSize,
                                                     @RequestParam("pageNo") Integer pageNo);

    @GetMapping("/account")
    AccountUser getAccountUser(@RequestParam String openId);

    @GetMapping("/forward/expired/unreceived")
    PageInfoDto<ForwardRecord> getExpiredUnReceivedForward(@RequestParam("pageSize") Integer pageSize,
                                                    @RequestParam("pageNo") Integer pageNo);

    @PostMapping("/forward/expire")
    Integer expireForward(@RequestBody ExpireForwardDto expireForwardDto);

    @PostMapping("/toc/statistics/execute")
    TocStatisticsDay executeStatistics(@RequestParam("startTime")Long startTime,@RequestParam("endTime")Long endTime);

    @GetMapping("/sys_config/list")
    List<SystemConfig> systemConfig(@RequestParam String namespace);

    // 邮件清理相关接口
    @GetMapping("/email/cleanup/success")
    List<EmailRecordDto> getSuccessEmailsForCleanup(@RequestParam("days") int days);

    @GetMapping("/email/cleanup/failed")
    List<EmailRecordDto> getFailedEmailsForCleanup(@RequestParam("days") int days);

    @GetMapping("/email/cleanup/by-email")
    List<EmailRecordDto> getEmailsForCleanupByEmail(@RequestParam("emailAddress") String emailAddress,
                                                    @RequestParam("days") int days,
                                                    @RequestParam("isSuccessOnly") boolean isSuccessOnly);

    @PostMapping("/email/cleanup/mark-cleared")
    Integer markEmailsAsCleared(@RequestBody List<Long> emailIds);

    @GetMapping("/email/cleanup/statistics")
    Integer getCleanupStatistics(@RequestParam("days") int days);

    @GetMapping("/email/cleanup/aifapiao-accounts")
    List<AccountUserEmail> getAifapiaoEmailAccounts();

    @PostMapping("/email/page/cus-exception")
    PageInfoDto<EmailRecordDto> getCusExceptionEmail(@RequestBody EmailFilter emailFilter);

}
