package com.guoranbot.scheduler.service.feign;

import com.guoranbot.common.dto.*;
import com.guoranbot.common.dto.toc.EmailFilter;
import com.guoranbot.common.po.*;
import com.guoranbot.common.po.toc.TocStatisticsDay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Data Service Feign客户端降级实现
 */
@Slf4j
@Component
public class IDataInfoServiceFallback implements IDataInfoService {

    @Override
    public PageInfoDto<InvoiceInfo> getUnVerifyInvoice(Integer pageSize, Integer pageNo) {
        log.warn("Data service unavailable, returning empty invoice page");
        return new PageInfoDto<>();
    }

    @Override
    public Integer update(List<InvoiceInfo> invoiceInfos) {
        log.warn("Data service unavailable, invoice update failed");
        return 0;
    }

    @Override
    public List<InvoiceInfo> getManualClassifyInvoice(Long startTime, Long endTime) {
        log.warn("Data service unavailable, returning empty manual classify invoice list");
        return new ArrayList<>();
    }

    @Override
    public Long getLatestTrainTime() {
        log.warn("Data service unavailable, returning current time as latest train time");
        return System.currentTimeMillis();
    }

    @Override
    public Long save(MlTrainRecord record) {
        log.warn("Data service unavailable, train record save failed");
        return null;
    }

    @Override
    public Integer checkSyncStatus() {
        log.warn("Data service unavailable, returning sync status as 0");
        return 0;
    }

    @Override
    public PageInfoDto<EmailRecordDto> getRetryEmailRecords(Integer pageSize, Integer pageNo) {
        log.warn("Data service unavailable, returning empty retry email page");
        return new PageInfoDto<>();
    }

    @Override
    public AccountUser getAccountUser(String openId) {
        log.warn("Data service unavailable, account user not found for openId: {}", openId);
        return null;
    }

    @Override
    public PageInfoDto<ForwardRecord> getExpiredUnReceivedForward(Integer pageSize, Integer pageNo) {
        log.warn("Data service unavailable, returning empty forward record page");
        return new PageInfoDto<>();
    }

    @Override
    public Integer expireForward(ExpireForwardDto expireForwardDto) {
        log.warn("Data service unavailable, forward expire failed");
        return 0;
    }

    @Override
    public TocStatisticsDay executeStatistics(Long startTime, Long endTime) {
        log.warn("Data service unavailable, statistics execution failed");
        return null;
    }

    @Override
    public List<SystemConfig> systemConfig(String namespace) {
        log.warn("Data service unavailable, returning empty system config list for namespace: {}", namespace);
        return new ArrayList<>();
    }

    @Override
    public List<EmailRecordDto> getSuccessEmailsForCleanup(int days) {
        log.warn("Data service unavailable, returning empty success emails list for cleanup");
        return new ArrayList<>();
    }

    @Override
    public List<EmailRecordDto> getFailedEmailsForCleanup(int days) {
        log.warn("Data service unavailable, returning empty failed emails list for cleanup");
        return new ArrayList<>();
    }

    @Override
    public List<EmailRecordDto> getEmailsForCleanupByEmail(String emailAddress, int days, boolean isSuccessOnly) {
        log.warn("Data service unavailable, returning empty emails list for cleanup by email: {}", emailAddress);
        return new ArrayList<>();
    }

    @Override
    public Integer markEmailsAsCleared(List<Long> emailIds) {
        log.warn("Data service unavailable, mark emails as cleared failed for {} emails", 
                emailIds != null ? emailIds.size() : 0);
        return 0;
    }

    @Override
    public Integer getCleanupStatistics(int days) {
        log.warn("Data service unavailable, returning 0 for cleanup statistics");
        return 0;
    }

    @Override
    public List<AccountUserEmail> getAifapiaoEmailAccounts() {
        log.error("Data service unavailable, cannot get AIFAPIAO email accounts - this will cause email cleanup to fail");
        return new ArrayList<>();
    }

    @Override
    public PageInfoDto<EmailRecordDto> getCusExceptionEmail(EmailFilter emailFilter) {
        log.warn("Data service unavailable, returning empty custom exception email page");
        return new PageInfoDto<>();
    }
}
