package com.guoranbot.scheduler.service.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 邮件历史数据分析服务FeignClient
 * 用于调用datasource模块的邮件历史分析服务
 *
 * 服务端点映射:
 * - data-service: /email/analysis/*
 *
 * 功能说明:
 * 1. 数据统计分析 - 获取邮件数量、类型、状态分布
 * 2. 时间分布分析 - 按时间段统计邮件分布情况
 * 3. 清理影响评估 - 评估不同时间范围的清理影响
 * 4. 清理计划生成 - 生成分阶段清理执行计划
 * 5. 用户分布分析 - 统计Top用户和发件人分布
 * 6. 风险评估分析 - 综合评估清理操作的风险等级
 */
@FeignClient(name = "data-service",
             contextId = "emailHistoryAnalysisService",
             configuration = EmailHistoryAnalysisFeignConfig.class,
             fallback = IEmailHistoryAnalysisServiceFallback.class)
public interface IEmailHistoryAnalysisService {

    /**
     * 获取邮件数量统计报告
     * @return 包含各种统计数据的Map
     */
    @GetMapping("/email/analysis/statistics")
    Map<String, Object> getEmailStatisticsReport();

    /**
     * 按时间段统计邮件数量分布
     * @return 时间段邮件数量分布
     */
    @GetMapping("/email/analysis/time-distribution")
    Map<String, Object> getEmailTimeDistribution();

    /**
     * 按处理状态统计邮件分布
     * @return 处理状态分布数据
     */
    @GetMapping("/email/analysis/status-distribution")
    Map<String, Object> getEmailStatusDistribution();

    /**
     * 按邮箱类型统计邮件分布
     * @return 邮箱类型分布数据
     */
    @GetMapping("/email/analysis/type-distribution")
    Map<String, Object> getEmailTypeDistribution();

    /**
     * 评估清理影响范围
     * @param months 要分析的月数（3个月、6个月等）
     * @return 清理影响评估结果
     */
    @GetMapping("/email/analysis/cleanup-impact/{months}")
    Map<String, Object> evaluateCleanupImpact(@PathVariable Integer months);

    /**
     * 生成分批清理计划
     * @return 清理计划详情
     */
    @GetMapping("/email/analysis/cleanup-plan")
    Map<String, Object> generateCleanupPlan();

    /**
     * 按用户统计邮件分布（Top N 用户）
     * @param topN 返回前N个用户
     * @return 用户邮件分布
     */
    @GetMapping("/email/analysis/top-users")
    List<Map<String, Object>> getTopUsersEmailDistribution(@RequestParam(defaultValue = "20") Integer topN);

    /**
     * 按发件人统计邮件分布（Top N 发件人）
     * @param topN 返回前N个发件人
     * @return 发件人邮件分布
     */
    @GetMapping("/email/analysis/top-senders")
    List<Map<String, Object>> getTopSendersDistribution(@RequestParam(defaultValue = "20") Integer topN);

    /**
     * 获取历史数据风险评估
     * @return 风险评估结果
     */
    @GetMapping("/email/analysis/risk-assessment")
    Map<String, Object> getRiskAssessment();

    /**
     * 生成数据分析报告
     * @return 完整的数据分析报告
     */
    @GetMapping("/email/analysis/full-report")
    Map<String, Object> generateAnalysisReport();
} 