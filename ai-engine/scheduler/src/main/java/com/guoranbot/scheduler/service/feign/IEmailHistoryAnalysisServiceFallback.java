package com.guoranbot.scheduler.service.feign;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件历史分析服务Feign客户端降级实现
 */
@Slf4j
@Component
public class IEmailHistoryAnalysisServiceFallback implements IEmailHistoryAnalysisService {

    @Override
    public Map<String, Object> getEmailStatisticsReport() {
        log.warn("Email history analysis service unavailable, returning empty statistics report");
        Map<String, Object> result = new HashMap<>();
        result.put("totalEmails", 0);
        result.put("successfulEmails", 0);
        result.put("failedEmails", 0);
        result.put("status", "SERVICE_UNAVAILABLE");
        return result;
    }

    @Override
    public Map<String, Object> getEmailTimeDistribution() {
        log.warn("Email history analysis service unavailable, returning empty time distribution");
        Map<String, Object> result = new HashMap<>();
        result.put("last30Days", 0);
        result.put("last90Days", 0);
        result.put("last180Days", 0);
        result.put("last365Days", 0);
        result.put("status", "SERVICE_UNAVAILABLE");
        return result;
    }

    @Override
    public Map<String, Object> getEmailStatusDistribution() {
        log.warn("Email history analysis service unavailable, returning empty status distribution");
        Map<String, Object> result = new HashMap<>();
        result.put("successful", 0);
        result.put("failed", 0);
        result.put("pending", 0);
        result.put("status", "SERVICE_UNAVAILABLE");
        return result;
    }

    @Override
    public Map<String, Object> getEmailTypeDistribution() {
        log.warn("Email history analysis service unavailable, returning empty type distribution");
        Map<String, Object> result = new HashMap<>();
        result.put("aifapiao", 0);
        result.put("other", 0);
        result.put("status", "SERVICE_UNAVAILABLE");
        return result;
    }

    @Override
    public Map<String, Object> evaluateCleanupImpact(Integer months) {
        log.error("Email history analysis service unavailable, cannot evaluate cleanup impact for {} months", months);
        Map<String, Object> result = new HashMap<>();
        result.put("totalEmailsToClean", 0);
        result.put("successfulEmailsToClean", 0);
        result.put("failedEmailsToClean", 0);
        result.put("estimatedStorageSaved", "0 MB");
        result.put("riskLevel", "UNKNOWN");
        result.put("status", "SERVICE_UNAVAILABLE");
        result.put("message", "Cannot evaluate cleanup impact - analysis service unavailable");
        return result;
    }

    @Override
    public Map<String, Object> generateCleanupPlan() {
        log.warn("Email history analysis service unavailable, returning empty cleanup plan");
        Map<String, Object> result = new HashMap<>();
        result.put("phases", new ArrayList<>());
        result.put("totalEstimatedTime", "Unknown");
        result.put("totalEmailsToProcess", 0);
        result.put("status", "SERVICE_UNAVAILABLE");
        return result;
    }

    @Override
    public List<Map<String, Object>> getTopUsersEmailDistribution(Integer topN) {
        log.warn("Email history analysis service unavailable, returning empty top users distribution");
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getTopSendersDistribution(Integer topN) {
        log.warn("Email history analysis service unavailable, returning empty top senders distribution");
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getRiskAssessment() {
        log.warn("Email history analysis service unavailable, returning default risk assessment");
        Map<String, Object> result = new HashMap<>();
        result.put("riskLevel", "UNKNOWN");
        result.put("riskFactors", new ArrayList<>());
        result.put("recommendations", new ArrayList<>());
        result.put("status", "SERVICE_UNAVAILABLE");
        return result;
    }

    @Override
    public Map<String, Object> generateAnalysisReport() {
        log.warn("Email history analysis service unavailable, returning empty analysis report");
        Map<String, Object> result = new HashMap<>();
        result.put("summary", "Analysis service unavailable");
        result.put("statistics", getEmailStatisticsReport());
        result.put("timeDistribution", getEmailTimeDistribution());
        result.put("statusDistribution", getEmailStatusDistribution());
        result.put("typeDistribution", getEmailTypeDistribution());
        result.put("riskAssessment", getRiskAssessment());
        result.put("status", "SERVICE_UNAVAILABLE");
        result.put("generatedAt", System.currentTimeMillis());
        return result;
    }
}
