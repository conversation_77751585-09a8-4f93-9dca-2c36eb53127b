package com.guoranbot.scheduler.service.impl;

import com.guoranbot.scheduler.service.EmailCleanupMonitoringService;
import com.guoranbot.scheduler.service.NotificationService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 邮箱清理监控服务实现类
 */
@Slf4j
@Service
public class EmailCleanupMonitoringServiceImpl implements EmailCleanupMonitoringService {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private NotificationService notificationService;

    // 配置参数 - 告警阈值
    @Value("${email.cleanup.monitoring.alerts.failure-rate-threshold:0.05}")
    private double failureRateThreshold;

    @Value("${email.cleanup.monitoring.alerts.slow-execution-threshold:900000}")
    private long slowExecutionThreshold;

    @Value("${email.cleanup.monitoring.alerts.max-active-tasks:3}")
    private int maxActiveTasks;

    @Value("${email.cleanup.monitoring.alerts.max-single-cleanup-emails:10000}")
    private int maxSingleCleanupEmails;

    // 监控指标
    private Counter cleanupTaskCounter;
    private Counter cleanupSuccessCounter;
    private Counter cleanupFailureCounter;
    private Counter imapErrorCounter;
    private Timer cleanupDurationTimer;
    private Gauge emailsProcessedGauge;
    private Gauge currentTasksGauge;
    private Counter singleTaskEmailCounter;  // 单次任务处理邮件数量计数器
    
    // 实时数据存储
    private final AtomicLong totalEmailsProcessed = new AtomicLong(0);
    private final AtomicInteger currentActiveTasks = new AtomicInteger(0);
    private final Map<String, TaskInfo> activeTasks = new ConcurrentHashMap<>();
    
    // 统计数据缓存键
    private static final String REDIS_KEY_PREFIX = "email_cleanup_monitoring:";
    private static final String STATS_KEY = REDIS_KEY_PREFIX + "stats:";
    private static final String ALERT_KEY = REDIS_KEY_PREFIX + "alert:";

    @PostConstruct
    public void init() {
        // 初始化监控指标
        this.cleanupTaskCounter = Counter.builder("email_cleanup_task_total")
                .description("Total number of email cleanup tasks")
                .register(meterRegistry);

        this.cleanupSuccessCounter = Counter.builder("email_cleanup_success_total")
                .description("Total number of successful email cleanups")
                .register(meterRegistry);

        this.cleanupFailureCounter = Counter.builder("email_cleanup_failure_total")
                .description("Total number of failed email cleanups")
                .register(meterRegistry);

        this.imapErrorCounter = Counter.builder("email_cleanup_imap_error_total")
                .description("Total number of IMAP connection errors")
                .register(meterRegistry);

        this.cleanupDurationTimer = Timer.builder("email_cleanup_duration")
                .description("Email cleanup task duration")
                .register(meterRegistry);

        this.emailsProcessedGauge = Gauge.builder("email_cleanup_emails_processed", totalEmailsProcessed, AtomicLong::get)
                .description("Total emails processed")
                .register(meterRegistry);

        this.currentTasksGauge = Gauge.builder("email_cleanup_active_tasks", currentActiveTasks, AtomicInteger::get)
                .description("Current active cleanup tasks")
                .register(meterRegistry);

        this.singleTaskEmailCounter = Counter.builder("email_cleanup_single_task_emails")
                .description("Number of emails processed in single task")
                .register(meterRegistry);

        log.info("邮箱清理监控服务初始化完成 - 告警阈值: 失败率{}%, 慢执行{}分钟, 最大并发{}个, 单次最大邮件{}封",
                failureRateThreshold * 100, slowExecutionThreshold / 60000, maxActiveTasks, maxSingleCleanupEmails);
    }

    @Override
    public void recordCleanupStart(String taskId, int emailCount) {
        log.info("记录清理任务开始 - 任务ID: {}, 邮件数量: {}", taskId, emailCount);

        cleanupTaskCounter.increment();
        currentActiveTasks.incrementAndGet();
        singleTaskEmailCounter.increment(emailCount);

        // 检查单次清理邮件数量告警
        if (emailCount > maxSingleCleanupEmails) {
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("taskId", taskId);
            alertData.put("emailCount", emailCount);
            alertData.put("threshold", maxSingleCleanupEmails);

            notificationService.sendAlert("EXCESSIVE_EMAIL_COUNT",
                    String.format("单次清理邮件数量过多: %d封 (阈值: %d封)", emailCount, maxSingleCleanupEmails),
                    "HIGH", alertData);
        }

        // 检查并发任务数告警
        if (currentActiveTasks.get() > maxActiveTasks) {
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("currentTasks", currentActiveTasks.get());
            alertData.put("threshold", maxActiveTasks);

            notificationService.sendAlert("TOO_MANY_CONCURRENT_TASKS",
                    String.format("并发清理任务过多: %d个 (阈值: %d个)", currentActiveTasks.get(), maxActiveTasks),
                    "MEDIUM", alertData);
        }

        TaskInfo taskInfo = new TaskInfo();
        taskInfo.taskId = taskId;
        taskInfo.emailCount = emailCount;
        taskInfo.startTime = System.currentTimeMillis();
        activeTasks.put(taskId, taskInfo);

        // 存储到Redis（如果可用）
        if (redisTemplate != null) {
            try {
                String key = STATS_KEY + "task:" + taskId;
                Map<String, Object> taskData = new HashMap<>();
                taskData.put("taskId", taskId);
                taskData.put("emailCount", emailCount);
                taskData.put("startTime", taskInfo.startTime);
                taskData.put("status", "RUNNING");
                redisTemplate.opsForHash().putAll(key, taskData);
                redisTemplate.expire(key, 24, TimeUnit.HOURS);
            } catch (Exception e) {
                log.warn("Redis存储失败，将继续执行但不记录到Redis: {}", e.getMessage());
            }
        } else {
            log.debug("Redis不可用，跳过任务数据存储");
        }
    }

    @Override
    public void recordCleanupComplete(String taskId, int successCount, int failedCount, long duration) {
        log.info("记录清理任务完成 - 任务ID: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                taskId, successCount, failedCount, duration);

        cleanupSuccessCounter.increment(successCount);
        if (failedCount > 0) {
            cleanupFailureCounter.increment(failedCount);
        }
        cleanupDurationTimer.record(duration, TimeUnit.MILLISECONDS);
        
        totalEmailsProcessed.addAndGet(successCount);
        currentActiveTasks.decrementAndGet();
        
        TaskInfo taskInfo = activeTasks.remove(taskId);
        if (taskInfo != null) {
            taskInfo.successCount = successCount;
            taskInfo.failedCount = failedCount;
            taskInfo.duration = duration;
            taskInfo.endTime = System.currentTimeMillis();
        }

        // 更新Redis
        String key = STATS_KEY + "task:" + taskId;
        Map<String, Object> updates = new HashMap<>();
        updates.put("successCount", successCount);
        updates.put("failedCount", failedCount);
        updates.put("duration", duration);
        updates.put("endTime", System.currentTimeMillis());
        updates.put("status", "COMPLETED");
        redisTemplate.opsForHash().putAll(key, updates);

        // 检查告警条件
        checkAndTriggerAlerts(successCount, failedCount, duration);
    }

    @Override
    public void recordCleanupFailure(String taskId, String errorMessage, long duration) {
        log.error("记录清理任务失败 - 任务ID: {}, 错误: {}, 耗时: {}ms", taskId, errorMessage, duration);

        cleanupFailureCounter.increment();
        cleanupDurationTimer.record(duration, TimeUnit.MILLISECONDS);
        currentActiveTasks.decrementAndGet();
        
        activeTasks.remove(taskId);

        // 更新Redis
        String key = STATS_KEY + "task:" + taskId;
        Map<String, Object> updates = new HashMap<>();
        updates.put("errorMessage", errorMessage);
        updates.put("duration", duration);
        updates.put("endTime", System.currentTimeMillis());
        updates.put("status", "FAILED");
        redisTemplate.opsForHash().putAll(key, updates);

        // 发送失败告警
        Map<String, Object> alertData = new HashMap<>();
        alertData.put("taskId", taskId);
        alertData.put("errorMessage", errorMessage);
        alertData.put("duration", duration);

        notificationService.sendAlert("TASK_FAILURE", "清理任务失败: " + errorMessage, "HIGH", alertData);
    }

    @Override
    public void recordImapConnectionError(String errorType, String errorMessage) {
        log.error("记录IMAP连接错误 - 类型: {}, 错误: {}", errorType, errorMessage);

        Counter.builder("email_cleanup_imap_error_total")
                .description("Total number of IMAP connection errors")
                .tag("error_type", errorType)
                .register(meterRegistry)
                .increment();

        // 发送IMAP错误告警
        Map<String, Object> alertData = new HashMap<>();
        alertData.put("errorType", errorType);
        alertData.put("errorMessage", errorMessage);

        notificationService.sendAlert("IMAP_ERROR", "IMAP连接错误 [" + errorType + "]: " + errorMessage, "CRITICAL", alertData);
    }

    @Override
    public Map<String, Object> getRealtimeMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        metrics.put("totalTasksExecuted", cleanupTaskCounter.count());
        metrics.put("totalSuccessCount", cleanupSuccessCounter.count());
        metrics.put("totalFailureCount", cleanupFailureCounter.count());
        metrics.put("totalImapErrors", imapErrorCounter.count());
        metrics.put("totalEmailsProcessed", totalEmailsProcessed.get());
        metrics.put("currentActiveTasks", currentActiveTasks.get());
        metrics.put("averageDuration", cleanupDurationTimer.mean(TimeUnit.MILLISECONDS));
        metrics.put("successRate", calculateSuccessRate());
        metrics.put("timestamp", System.currentTimeMillis());

        return metrics;
    }

    @Override
    public Map<String, Object> getCleanupStatistics(int hours) {
        Map<String, Object> stats = new HashMap<>();
        
        long startTime = System.currentTimeMillis() - (hours * 3600000L);
        
        // 从Redis获取指定时间段的统计数据
        String pattern = STATS_KEY + "task:*";
        Set<String> keys = redisTemplate.keys(pattern);
        
        int totalTasks = 0;
        int totalSuccess = 0;
        int totalFailure = 0;
        long totalDuration = 0;
        int completedTasks = 0;

        if (keys != null) {
            for (String key : keys) {
                Map<Object, Object> taskData = redisTemplate.opsForHash().entries(key);
                
                Long taskStartTime = (Long) taskData.get("startTime");
                if (taskStartTime != null && taskStartTime >= startTime) {
                    totalTasks++;
                    
                    String status = (String) taskData.get("status");
                    if ("COMPLETED".equals(status)) {
                        completedTasks++;
                        Integer success = (Integer) taskData.get("successCount");
                        Integer failure = (Integer) taskData.get("failedCount");
                        Long duration = (Long) taskData.get("duration");
                        
                        totalSuccess += (success != null ? success : 0);
                        totalFailure += (failure != null ? failure : 0);
                        totalDuration += (duration != null ? duration : 0);
                    }
                }
            }
        }

        stats.put("period", hours + "小时");
        stats.put("totalTasks", totalTasks);
        stats.put("completedTasks", completedTasks);
        stats.put("totalSuccessCount", totalSuccess);
        stats.put("totalFailureCount", totalFailure);
        stats.put("averageDuration", completedTasks > 0 ? totalDuration / completedTasks : 0);
        stats.put("successRate", totalSuccess + totalFailure > 0 ? 
                (double) totalSuccess / (totalSuccess + totalFailure) : 0.0);
        stats.put("generatedAt", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return stats;
    }

    @Override
    public Map<String, Object> checkAlertConditions() {
        Map<String, Object> alerts = new HashMap<>();
        List<Map<String, Object>> activeAlerts = new ArrayList<>();

        // 检查失败率
        double failureRate = calculateFailureRate();
        if (failureRate > failureRateThreshold) {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "HIGH_FAILURE_RATE");
            alert.put("severity", "HIGH");
            alert.put("message", String.format("清理失败率过高: %.2f%% (阈值: %.2f%%)", failureRate * 100, failureRateThreshold * 100));
            alert.put("timestamp", System.currentTimeMillis());
            alert.put("currentRate", failureRate);
            alert.put("threshold", failureRateThreshold);
            activeAlerts.add(alert);
        }

        // 检查执行时间
        double avgDuration = cleanupDurationTimer.mean(TimeUnit.MILLISECONDS);
        if (avgDuration > slowExecutionThreshold) {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "SLOW_EXECUTION");
            alert.put("severity", "MEDIUM");
            alert.put("message", String.format("清理任务执行缓慢: 平均%.2f分钟 (阈值: %.2f分钟)",
                    avgDuration / 60000, slowExecutionThreshold / 60000.0));
            alert.put("timestamp", System.currentTimeMillis());
            alert.put("currentDuration", avgDuration);
            alert.put("threshold", slowExecutionThreshold);
            activeAlerts.add(alert);
        }

        // 检查活跃任务数
        if (currentActiveTasks.get() > maxActiveTasks) {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "TOO_MANY_TASKS");
            alert.put("severity", "MEDIUM");
            alert.put("message", String.format("并发清理任务过多: %d个 (阈值: %d个)", currentActiveTasks.get(), maxActiveTasks));
            alert.put("timestamp", System.currentTimeMillis());
            alert.put("currentTasks", currentActiveTasks.get());
            alert.put("threshold", maxActiveTasks);
            activeAlerts.add(alert);
        }

        alerts.put("activeAlerts", activeAlerts);
        alerts.put("totalAlerts", activeAlerts.size());
        alerts.put("checkTime", System.currentTimeMillis());

        return alerts;
    }

    @Override
    public void sendAlert(String alertType, String message, String severity) {
        log.warn("【告警】类型: {}, 级别: {}, 消息: {}", alertType, severity, message);

        // 存储告警到Redis
        String alertKey = ALERT_KEY + alertType + ":" + System.currentTimeMillis();
        Map<String, Object> alertData = new HashMap<>();
        alertData.put("type", alertType);
        alertData.put("message", message);
        alertData.put("severity", severity);
        alertData.put("timestamp", System.currentTimeMillis());
        alertData.put("resolved", false);

        if (redisTemplate != null) {
            try {
                redisTemplate.opsForHash().putAll(alertKey, alertData);
                redisTemplate.expire(alertKey, 7, TimeUnit.DAYS);
            } catch (Exception e) {
                log.warn("Redis存储告警失败: {}", e.getMessage());
            }
        }

        // 使用新的通知服务发送告警
        try {
            notificationService.sendAlert(alertType, message, severity, alertData);
        } catch (Exception e) {
            log.error("发送告警通知失败", e);
            // 降级处理：至少记录到日志
            switch (severity) {
                case "CRITICAL":
                    log.error("【紧急告警】{}: {}", alertType, message);
                    break;
                case "HIGH":
                    log.warn("【高级告警】{}: {}", alertType, message);
                    break;
                case "MEDIUM":
                    log.info("【中级告警】{}: {}", alertType, message);
                    break;
                default:
                    log.info("【低级告警】{}: {}", alertType, message);
                    break;
            }
        }
    }

    @Override
    public Map<String, Object> getMonitoringDashboard() {
        Map<String, Object> dashboard = new HashMap<>();

        // 实时指标
        dashboard.put("realtimeMetrics", getRealtimeMetrics());

        // 短期统计 (最近24小时)
        dashboard.put("last24Hours", getCleanupStatistics(24));

        // 活跃告警
        dashboard.put("alerts", checkAlertConditions());

        // 系统状态
        Map<String, Object> systemStatus = new HashMap<>();
        systemStatus.put("status", currentActiveTasks.get() < 5 ? "HEALTHY" : "BUSY");
        systemStatus.put("uptime", System.currentTimeMillis()); // 简化的运行时间
        systemStatus.put("version", "1.0.0");
        dashboard.put("systemStatus", systemStatus);

        // 性能趋势 (简化版)
        Map<String, Object> trends = new HashMap<>();
        trends.put("successRateTrend", calculateSuccessRate());
        trends.put("avgDurationTrend", cleanupDurationTimer.mean(TimeUnit.MILLISECONDS));
        dashboard.put("trends", trends);

        dashboard.put("generatedAt", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return dashboard;
    }

    private double calculateSuccessRate() {
        double totalSuccess = cleanupSuccessCounter.count();
        double totalFailure = cleanupFailureCounter.count();
        return totalSuccess + totalFailure > 0 ? totalSuccess / (totalSuccess + totalFailure) : 1.0;
    }

    private double calculateFailureRate() {
        return 1.0 - calculateSuccessRate();
    }

    private void checkAndTriggerAlerts(int successCount, int failedCount, long duration) {
        // 检查单次任务的告警条件

        // 检查单次任务失败率
        if (successCount + failedCount > 0) {
            double taskFailureRate = (double) failedCount / (successCount + failedCount);
            if (taskFailureRate > failureRateThreshold) {
                Map<String, Object> alertData = new HashMap<>();
                alertData.put("successCount", successCount);
                alertData.put("failedCount", failedCount);
                alertData.put("taskFailureRate", taskFailureRate);

                notificationService.sendAlert("TASK_HIGH_FAILURE",
                        String.format("单次任务失败率过高: %.2f%% (成功%d, 失败%d)", taskFailureRate * 100, successCount, failedCount),
                        "HIGH", alertData);
            }
        }

        // 检查单次任务执行时间
        if (duration > slowExecutionThreshold) {
            Map<String, Object> alertData = new HashMap<>();
            alertData.put("duration", duration);
            alertData.put("threshold", slowExecutionThreshold);

            notificationService.sendAlert("TASK_SLOW_EXECUTION",
                    String.format("任务执行时间过长: %.2f分钟 (阈值: %.2f分钟)",
                            duration / 60000.0, slowExecutionThreshold / 60000.0),
                    "MEDIUM", alertData);
        }
    }

    // 内部类：任务信息
    private static class TaskInfo {
        String taskId;
        int emailCount;
        long startTime;
        long endTime;
        int successCount;
        int failedCount;
        long duration;
    }
} 