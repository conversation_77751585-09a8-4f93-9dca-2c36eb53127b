package com.guoranbot.scheduler.service.impl;

import com.guoranbot.common.dto.EmailRecordDto;
import com.guoranbot.common.po.AccountUserEmail;
import com.guoranbot.scheduler.cache.EmailAccountCache;
import com.guoranbot.scheduler.service.IEmailCleanupService;
import com.guoranbot.scheduler.service.ServiceHealthChecker;
import com.guoranbot.scheduler.service.feign.IDataInfoService;
import com.guoranbot.scheduler.service.feign.IEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 邮箱清理服务实现
 */
@Slf4j
@Service
public class EmailCleanupServiceImpl implements IEmailCleanupService {

    @Autowired
    private IDataInfoService dataInfoService;

    @Autowired
    private IEmailService emailService;

    @Autowired(required = false)
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ServiceHealthChecker serviceHealthChecker;

    @Autowired
    private EmailAccountCache emailAccountCache;

    @Value("${email.cleanup.success.days:1}")
    private int successCleanupDays;

    @Value("${email.cleanup.failed.days:3}")
    private int failedCleanupDays;

    @Value("${email.cleanup.batch.size:50}")
    private int batchSize;

    @Value("${email.cleanup.enabled:true}")
    private boolean cleanupEnabled;

    private static final String CLEANUP_LOCK_KEY = "email_cleanup_lock";
    private static final int LOCK_TIMEOUT_MINUTES = 60;

    @Override
    public void executeScheduledCleanup() {
        if (!cleanupEnabled) {
            log.info("Email cleanup is disabled, skipping scheduled cleanup");
            return;
        }

        // 获取分布式锁，防止重复执行（如果Redis可用）
        String lockValue = String.valueOf(System.currentTimeMillis());
        boolean lockAcquired = true;

        if (redisTemplate != null) {
            try {
                Boolean acquired = redisTemplate.opsForValue().setIfAbsent(
                    CLEANUP_LOCK_KEY, lockValue, LOCK_TIMEOUT_MINUTES, TimeUnit.MINUTES);
                lockAcquired = Boolean.TRUE.equals(acquired);

                if (!lockAcquired) {
                    log.info("Email cleanup task is already running, skipping this execution");
                    return;
                }
            } catch (Exception e) {
                log.warn("Redis分布式锁获取失败，将继续执行但可能重复: {}", e.getMessage());
                lockAcquired = true; // 继续执行
            }
        } else {
            log.debug("Redis不可用，跳过分布式锁检查");
        }

        try {
            log.info("Starting scheduled email cleanup task");
            long startTime = System.currentTimeMillis();

            // T+1清理成功邮件
            CleanupResult successResult = cleanupSuccessfulEmails(successCleanupDays);
            log.info("T+1 cleanup result: {}", successResult);

            // T+3清理失败邮件
            CleanupResult failedResult = cleanupFailedEmails(failedCleanupDays);
            log.info("T+3 cleanup result: {}", failedResult);

            long duration = System.currentTimeMillis() - startTime;
            log.info("Scheduled email cleanup completed in {}ms. Success: {}, Failed: {}", 
                     duration, successResult.getSuccessCount(), failedResult.getSuccessCount());

        } catch (Exception e) {
            log.error("Error during scheduled email cleanup", e);
        } finally {
            // 释放锁（如果Redis可用且获取了锁）
            if (redisTemplate != null && lockAcquired) {
                try {
                    String currentValue = redisTemplate.opsForValue().get(CLEANUP_LOCK_KEY);
                    if (lockValue.equals(currentValue)) {
                        redisTemplate.delete(CLEANUP_LOCK_KEY);
                    }
                } catch (Exception e) {
                    log.warn("Redis锁释放失败: {}", e.getMessage());
                }
            }
        }
    }

    @Override
    public CleanupResult cleanupSuccessfulEmails(int days) {
        log.info("Starting cleanup of successful emails older than {} days", days);
        long startTime = System.currentTimeMillis();

        // 执行服务健康检查
        ServiceHealthChecker.HealthReport healthReport = serviceHealthChecker.getHealthReport();
        if (!healthReport.isOverallHealthy()) {
            String errorMsg = String.format("Service health check failed: %s", healthReport);
            log.error(errorMsg);
            return new CleanupResult(0, 0, 1, System.currentTimeMillis() - startTime, errorMsg);
        }

        try {
            List<EmailRecordDto> emailsToCleanup = dataInfoService.getSuccessEmailsForCleanup(days);
            if (emailsToCleanup == null || emailsToCleanup.isEmpty()) {
                log.info("No successful emails to cleanup");
                return new CleanupResult(0, 0, 0, System.currentTimeMillis() - startTime, "No emails to cleanup");
            }

            log.info("Found {} successful emails to cleanup", emailsToCleanup.size());
            return processEmailCleanup(emailsToCleanup, true, startTime);

        } catch (Exception e) {
            log.error("Error cleaning up successful emails", e);
            return new CleanupResult(0, 0, 1, System.currentTimeMillis() - startTime, "Error: " + e.getMessage());
        }
    }

    @Override
    public CleanupResult cleanupFailedEmails(int days) {
        log.info("Starting cleanup of failed emails older than {} days", days);
        long startTime = System.currentTimeMillis();

        try {
            List<EmailRecordDto> emailsToCleanup = dataInfoService.getFailedEmailsForCleanup(days);
            if (emailsToCleanup == null || emailsToCleanup.isEmpty()) {
                log.info("No failed emails to cleanup");
                return new CleanupResult(0, 0, 0, System.currentTimeMillis() - startTime, "No emails to cleanup");
            }

            log.info("Found {} failed emails to cleanup", emailsToCleanup.size());
            return processEmailCleanup(emailsToCleanup, false, startTime);

        } catch (Exception e) {
            log.error("Error cleaning up failed emails", e);
            return new CleanupResult(0, 0, 1, System.currentTimeMillis() - startTime, "Error: " + e.getMessage());
        }
    }

    @Override
    public CleanupResult cleanupEmailsByAddress(String emailAddress, int days, boolean isSuccessOnly) {
        if (!isAifapiaoEmail(emailAddress)) {
            log.warn("Email cleanup rejected for non-aifapiao email: {}", emailAddress);
            return new CleanupResult(0, 0, 1, 0, "Only @aifapiao.com emails are allowed");
        }

        log.info("Starting cleanup for email: {}, days: {}, successOnly: {}", emailAddress, days, isSuccessOnly);
        long startTime = System.currentTimeMillis();

        try {
            List<EmailRecordDto> emailsToCleanup = dataInfoService.getEmailsForCleanupByEmail(emailAddress, days, isSuccessOnly);
            if (emailsToCleanup == null || emailsToCleanup.isEmpty()) {
                log.info("No emails to cleanup for address: {}", emailAddress);
                return new CleanupResult(0, 0, 0, System.currentTimeMillis() - startTime, "No emails to cleanup");
            }

            log.info("Found {} emails to cleanup for address: {}", emailsToCleanup.size(), emailAddress);
            return processEmailCleanup(emailsToCleanup, isSuccessOnly, startTime);

        } catch (Exception e) {
            log.error("Error cleaning up emails for address: {}", emailAddress, e);
            return new CleanupResult(0, 0, 1, System.currentTimeMillis() - startTime, "Error: " + e.getMessage());
        }
    }

    @Override
    public List<EmailRecordDto> previewCleanup(String emailAddress, int days, boolean isSuccessOnly) {
        if (!isAifapiaoEmail(emailAddress)) {
            log.warn("Preview cleanup rejected for non-aifapiao email: {}", emailAddress);
            return new ArrayList<>();
        }

        try {
            return dataInfoService.getEmailsForCleanupByEmail(emailAddress, days, isSuccessOnly);
        } catch (Exception e) {
            log.error("Error previewing cleanup for email: {}", emailAddress, e);
            return new ArrayList<>();
        }
    }

    @Override
    public CleanupStatistics getCleanupStatistics(int days) {
        try {
            List<EmailRecordDto> successEmails = dataInfoService.getSuccessEmailsForCleanup(days);
            List<EmailRecordDto> failedEmails = dataInfoService.getFailedEmailsForCleanup(days);
            Integer alreadyCleared = dataInfoService.getCleanupStatistics(days);

            int successCount = successEmails != null ? successEmails.size() : 0;
            int failedCount = failedEmails != null ? failedEmails.size() : 0;
            int totalCount = successCount + failedCount;
            int clearedCount = alreadyCleared != null ? alreadyCleared : 0;

            return new CleanupStatistics(totalCount, successCount, failedCount, clearedCount);

        } catch (Exception e) {
            log.error("Error getting cleanup statistics", e);
            return new CleanupStatistics(0, 0, 0, 0);
        }
    }

    /**
     * 处理邮件清理
     */
    private CleanupResult processEmailCleanup(List<EmailRecordDto> emailsToCleanup, boolean isSuccessOnly, long startTime) {
        // 按邮箱地址分组
        java.util.Map<String, List<EmailRecordDto>> emailGroups = emailsToCleanup.stream()
            .filter(email -> isAifapiaoEmail(email.getEmail()))
            .collect(Collectors.groupingBy(EmailRecordDto::getEmail));

        int totalProcessed = 0;
        int successCount = 0;
        int failedCount = 0;

        for (java.util.Map.Entry<String, List<EmailRecordDto>> entry : emailGroups.entrySet()) {
            String emailAddress = entry.getKey();
            List<EmailRecordDto> emails = entry.getValue();

            log.info("Processing {} emails for address: {}", emails.size(), emailAddress);

            try {
                // 获取邮箱账户信息
                AccountUserEmail accountEmail = getAccountEmailInfo(emailAddress);
                if (accountEmail == null) {
                    log.warn("Cannot find account info for email: {}", emailAddress);
                    failedCount++;
                    continue;
                }

                // 提取messageUIDs
                List<String> messageUIDs = emails.stream()
                    .map(EmailRecordDto::getUid)
                    .filter(uid -> !StringUtils.isEmpty(uid))
                    .collect(Collectors.toList());

                if (messageUIDs.isEmpty()) {
                    log.warn("No valid message UIDs found for email: {}", emailAddress);
                    continue;
                }

                // 调用email服务删除邮件
                IEmailService.DeleteProcessedEmailsRequest request = new IEmailService.DeleteProcessedEmailsRequest();
                request.setEmailAddress(emailAddress);
                request.setEmailPassword(accountEmail.decodePassword());
                request.setDays(isSuccessOnly ? successCleanupDays : failedCleanupDays);
                request.setIsSuccessOnly(isSuccessOnly);

                Boolean deleteResult = emailService.deleteProcessedEmails(request);
                
                totalProcessed += emails.size();
                if (Boolean.TRUE.equals(deleteResult)) {
                    successCount++;
                    log.info("Successfully cleaned up {} emails for address: {}", emails.size(), emailAddress);
                } else {
                    failedCount++;
                    log.error("Failed to cleanup emails for address: {}", emailAddress);
                }

                // 避免过度负载，批次间暂停
                Thread.sleep(100);

            } catch (Exception e) {
                log.error("Error processing emails for address: {}", emailAddress, e);
                failedCount++;
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        String message = String.format("Processed %d emails, %d successful, %d failed", 
                                      totalProcessed, successCount, failedCount);
        
        return new CleanupResult(totalProcessed, successCount, failedCount, duration, message);
    }

    /**
     * 获取邮箱账户信息（优化版本，使用缓存）
     */
    private AccountUserEmail getAccountEmailInfo(String emailAddress) {
        try {
            log.debug("Attempting to get account info for email: {}", emailAddress);

            // 使用缓存获取账户信息，避免频繁调用远程服务
            AccountUserEmail account = emailAccountCache.getAccountByEmail(emailAddress);

            if (account == null) {
                log.warn("No account found for email: {} in cache", emailAddress);

                // 记录缓存统计信息用于调试
                EmailAccountCache.CacheStats stats = emailAccountCache.getCacheStats();
                log.debug("Cache stats: {}", stats);

                // 如果缓存中没有找到，尝试刷新缓存后再次查找
                log.info("Refreshing cache and retrying for email: {}", emailAddress);
                emailAccountCache.refreshCache();
                account = emailAccountCache.getAccountByEmail(emailAddress);

                if (account == null) {
                    log.warn("Still no account found for email: {} after cache refresh", emailAddress);
                }
            } else {
                log.debug("Successfully found account for email: {} from cache", emailAddress);
            }

            return account;

        } catch (Exception e) {
            log.error("Unexpected error getting account info for email: {}, falling back to direct service call", emailAddress, e);

            // 降级处理：如果缓存出现问题，直接调用数据服务
            try {
                List<AccountUserEmail> aifapiaoAccounts = dataInfoService.getAifapiaoEmailAccounts();
                if (aifapiaoAccounts != null && !aifapiaoAccounts.isEmpty()) {
                    return aifapiaoAccounts.stream()
                        .filter(acc -> emailAddress.equals(acc.getEmail()))
                        .findFirst()
                        .orElse(null);
                }
            } catch (Exception fallbackException) {
                log.error("Fallback service call also failed for email: {}", emailAddress, fallbackException);
            }

            return null;
        }
    }

    /**
     * 检查是否是AIFAPIAO邮箱
     */
    private boolean isAifapiaoEmail(String email) {
        return email != null && email.toLowerCase().endsWith("@aifapiao.com");
    }
} 