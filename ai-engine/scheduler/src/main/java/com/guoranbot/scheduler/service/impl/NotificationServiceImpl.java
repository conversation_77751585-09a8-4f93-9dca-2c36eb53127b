package com.guoranbot.scheduler.service.impl;

import com.guoranbot.scheduler.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 通知服务实现类
 * 集成钉钉、邮件、短信等多种通知方式
 */
@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    @Autowired(required = false)
    private JavaMailSender mailSender;

    // 配置参数
    @Value("${email.cleanup.monitoring.notification.enabled:true}")
    private boolean notificationEnabled;

    @Value("${email.cleanup.monitoring.notification.recipients.email:}")
    private List<String> defaultEmailRecipients;

    @Value("${email.cleanup.monitoring.notification.recipients.dingtalk.userIds:}")
    private List<String> defaultDingTalkUserIds;

    @Value("${email.cleanup.monitoring.notification.recipients.sms.phones:}")
    private List<String> defaultSmsPhones;

    @Value("${DINGTALK_WEBHOOK_URL:}")
    private String dingTalkWebhookUrl;

    @Value("${DINGTALK_SECRET:}")
    private String dingTalkSecret;

    @Value("${spring.mail.username:}")
    private String mailFrom;

    // 通知统计
    private final AtomicLong totalNotificationsSent = new AtomicLong(0);
    private final Map<String, AtomicLong> channelStatistics = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> severityStatistics = new ConcurrentHashMap<>();

    @Override
    public void sendAlert(String alertType, String message, String severity, Map<String, Object> additionalData) {
        if (!notificationEnabled) {
            log.debug("通知服务已禁用，跳过告警通知");
            return;
        }

        log.info("发送告警通知 - 类型: {}, 级别: {}, 消息: {}", alertType, severity, message);

        List<String> channels = getNotificationChannels(severity);
        String formattedMessage = formatAlertMessage(alertType, message, severity, additionalData);

        for (String channel : channels) {
            try {
                switch (channel.toLowerCase()) {
                    case "dingtalk":
                        sendDingTalkAlert(formattedMessage, severity);
                        break;
                    case "email":
                        sendEmailAlert(alertType, formattedMessage, severity);
                        break;
                    case "sms":
                        sendSmsAlert(message, severity);
                        break;
                    case "console":
                        log.warn("【控制台告警】{}: {}", alertType, message);
                        break;
                    case "log":
                        log.info("【日志告警】{}: {}", alertType, message);
                        break;
                    default:
                        log.warn("不支持的通知渠道: {}", channel);
                }
                
                // 更新统计
                channelStatistics.computeIfAbsent(channel, k -> new AtomicLong(0)).incrementAndGet();
                totalNotificationsSent.incrementAndGet();
                
            } catch (Exception e) {
                log.error("发送{}通知失败: {}", channel, e.getMessage(), e);
            }
        }

        // 更新级别统计
        severityStatistics.computeIfAbsent(severity, k -> new AtomicLong(0)).incrementAndGet();
    }

    @Override
    public void sendDingTalkNotification(String message, List<String> userIds, boolean isAtAll) {
        if (!isChannelAvailable("dingtalk")) {
            log.warn("钉钉通知服务不可用");
            return;
        }

        try {
            // 这里集成钉钉MCP工具
            // 由于MCP工具在运行时环境中，这里提供接口供外部调用
            log.info("发送钉钉通知: {}, 接收人: {}, @所有人: {}", message, userIds, isAtAll);
            
            // TODO: 实际集成钉钉MCP工具的调用
            // 可以通过Spring事件或者直接调用MCP工具
            
        } catch (Exception e) {
            log.error("发送钉钉通知失败", e);
            throw new RuntimeException("钉钉通知发送失败", e);
        }
    }

    @Override
    public void sendEmailNotification(String subject, String content, List<String> recipients, boolean isHtml) {
        if (!isChannelAvailable("email")) {
            log.warn("邮件通知服务不可用");
            return;
        }

        try {
            if (isHtml) {
                MimeMessage mimeMessage = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
                helper.setFrom(mailFrom);
                helper.setTo(recipients.toArray(new String[0]));
                helper.setSubject(subject);
                helper.setText(content, true);
                mailSender.send(mimeMessage);
            } else {
                SimpleMailMessage mailMessage = new SimpleMailMessage();
                mailMessage.setFrom(mailFrom);
                mailMessage.setTo(recipients.toArray(new String[0]));
                mailMessage.setSubject(subject);
                mailMessage.setText(content);
                mailSender.send(mailMessage);
            }
            
            log.info("邮件通知发送成功 - 收件人: {}, 主题: {}", recipients, subject);
            
        } catch (Exception e) {
            log.error("发送邮件通知失败", e);
            throw new RuntimeException("邮件通知发送失败", e);
        }
    }

    @Override
    public void sendSmsNotification(String message, List<String> phoneNumbers, String templateCode) {
        if (!isChannelAvailable("sms")) {
            log.warn("短信通知服务不可用");
            return;
        }

        try {
            // 这里集成现有的SMS服务
            log.info("发送短信通知: {}, 手机号: {}, 模板: {}", message, phoneNumbers, templateCode);
            
            // TODO: 调用现有的SMS服务
            // 可以通过Feign客户端调用SMS服务
            
        } catch (Exception e) {
            log.error("发送短信通知失败", e);
            throw new RuntimeException("短信通知发送失败", e);
        }
    }

    @Override
    public List<String> getNotificationChannels(String severity) {
        // 根据告警级别返回对应的通知渠道
        switch (severity.toUpperCase()) {
            case "CRITICAL":
                return Arrays.asList("dingtalk", "sms", "email");
            case "HIGH":
                return Arrays.asList("dingtalk", "email");
            case "MEDIUM":
                return Arrays.asList("dingtalk");
            case "LOW":
            default:
                return Arrays.asList("log");
        }
    }

    @Override
    public boolean isChannelAvailable(String channel) {
        switch (channel.toLowerCase()) {
            case "dingtalk":
                return dingTalkWebhookUrl != null && !dingTalkWebhookUrl.isEmpty();
            case "email":
                return mailSender != null && mailFrom != null && !mailFrom.isEmpty();
            case "sms":
                return true; // 假设SMS服务总是可用
            case "console":
            case "log":
                return true;
            default:
                return false;
        }
    }

    @Override
    public Map<String, Object> getNotificationStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalNotificationsSent", totalNotificationsSent.get());
        stats.put("channelStatistics", channelStatistics);
        stats.put("severityStatistics", severityStatistics);
        stats.put("availableChannels", getAvailableChannels());
        stats.put("lastUpdated", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return stats;
    }

    @Override
    public boolean testNotification(String channel, String testMessage) {
        try {
            String message = "【测试通知】" + testMessage + " - " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            switch (channel.toLowerCase()) {
                case "dingtalk":
                    sendDingTalkNotification(message, defaultDingTalkUserIds, false);
                    break;
                case "email":
                    sendEmailNotification("邮箱清理监控测试", message, defaultEmailRecipients, false);
                    break;
                case "sms":
                    sendSmsNotification(message, defaultSmsPhones, "SMS_TEST_TEMPLATE");
                    break;
                default:
                    log.info("测试通知 - {}: {}", channel, message);
            }
            
            return true;
        } catch (Exception e) {
            log.error("测试{}通知失败", channel, e);
            return false;
        }
    }

    private void sendDingTalkAlert(String message, String severity) {
        List<String> userIds = defaultDingTalkUserIds;
        boolean isAtAll = "CRITICAL".equals(severity);
        sendDingTalkNotification(message, userIds, isAtAll);
    }

    private void sendEmailAlert(String alertType, String message, String severity) {
        String subject = String.format("[%s] 邮箱清理监控告警 - %s", severity, alertType);
        sendEmailNotification(subject, message, defaultEmailRecipients, false);
    }

    private void sendSmsAlert(String message, String severity) {
        if ("CRITICAL".equals(severity)) {
            // 只有紧急告警才发送短信
            sendSmsNotification(message, defaultSmsPhones, "SMS_ALERT_TEMPLATE");
        }
    }

    private String formatAlertMessage(String alertType, String message, String severity, Map<String, Object> additionalData) {
        StringBuilder sb = new StringBuilder();
        sb.append("【邮箱清理监控告警】\n");
        sb.append("告警类型: ").append(alertType).append("\n");
        sb.append("告警级别: ").append(severity).append("\n");
        sb.append("告警消息: ").append(message).append("\n");
        sb.append("告警时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        
        if (additionalData != null && !additionalData.isEmpty()) {
            sb.append("附加信息:\n");
            additionalData.forEach((key, value) -> 
                sb.append("  ").append(key).append(": ").append(value).append("\n"));
        }
        
        return sb.toString();
    }

    private List<String> getAvailableChannels() {
        List<String> available = new ArrayList<>();
        if (isChannelAvailable("dingtalk")) available.add("dingtalk");
        if (isChannelAvailable("email")) available.add("email");
        if (isChannelAvailable("sms")) available.add("sms");
        available.add("console");
        available.add("log");
        return available;
    }
}
