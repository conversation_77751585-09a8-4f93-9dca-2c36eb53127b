package com.guoranbot.scheduler.task;

import com.guoranbot.scheduler.service.IBatchEmailCleanupService;
import com.guoranbot.scheduler.service.EmailCleanupMonitoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 分批邮件清理定时任务
 * 
 * 执行策略:
 * - 三阶段分批清理历史邮件
 * - 第一阶段：3个月前成功邮件（风险低）
 * - 第二阶段：6个月前所有邮件（风险中）
 * - 第三阶段：1年前历史邮件（彻底清理）
 * 
 * 定时执行: 每月第一个周日凌晨3点执行（避免与日常清理冲突）
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "email.batch.cleanup.enabled", havingValue = "true", matchIfMissing = true)
public class BatchEmailCleanupTask {

    @Autowired
    private IBatchEmailCleanupService batchCleanupService;

    @Autowired
    private EmailCleanupMonitoringService monitoringService;

    /**
     * 定时执行分批邮件清理任务
     * 每月1号凌晨3点执行（简化版本，避免复杂的Cron表达式）
     * Cron表达式: 秒 分 时 日 月 周
     * 0 0 3 1 * ? = 每月1号凌晨3点
     */
    @Scheduled(cron = "0 0 3 1 * ?")
    public void executeScheduledBatchCleanup() {
        long startTime = System.currentTimeMillis();
        
        log.info("【定时分批清理】开始执行月度分批邮件清理任务");
        
        try {
            // 检查是否已有任务在运行
            IBatchEmailCleanupService.BatchCleanupStatus status = batchCleanupService.getBatchCleanupStatus();
            if (status != null && "RUNNING".equals(status.getStatus())) {
                log.warn("【定时分批清理】已有分批清理任务正在执行，跳过本次执行");
                return;
            }

            // 获取清理计划
            IBatchEmailCleanupService.BatchCleanupPlan plan = batchCleanupService.getBatchCleanupPlan();
            log.info("【定时分批清理】清理计划：预计处理{}封邮件，耗时{}", 
                    plan.getEstimatedTotalEmails(), plan.getEstimatedTotalTime());

            // 执行完整的三阶段分批清理
            IBatchEmailCleanupService.BatchCleanupResult result = batchCleanupService.executeFullBatchCleanup();
            
            long duration = System.currentTimeMillis() - startTime;
            
            if ("SUCCESS".equals(result.getStatus())) {
                log.info("【定时分批清理】月度分批清理执行成功 - 批次ID: {}, 处理邮件: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                        result.getBatchId(), result.getTotalEmailsProcessed(), 
                        result.getSuccessCount(), result.getFailedCount(), duration);
                
                // 发送成功通知
                monitoringService.sendAlert("MONTHLY_BATCH_CLEANUP_SUCCESS", 
                        String.format("月度分批清理成功完成，处理%d封邮件，成功%d封，失败%d封", 
                                result.getTotalEmailsProcessed(), result.getSuccessCount(), result.getFailedCount()), 
                        "INFO");
                        
            } else if ("PAUSED".equals(result.getStatus())) {
                log.warn("【定时分批清理】月度分批清理被暂停 - 批次ID: {}, 已完成阶段: {}/{}", 
                        result.getBatchId(), result.getCompletedPhases(), result.getTotalPhases());
                
                // 发送暂停通知
                monitoringService.sendAlert("MONTHLY_BATCH_CLEANUP_PAUSED", 
                        String.format("月度分批清理被暂停，已完成%d/%d个阶段", 
                                result.getCompletedPhases(), result.getTotalPhases()), 
                        "MEDIUM");
                        
            } else {
                log.error("【定时分批清理】月度分批清理执行失败 - 批次ID: {}, 错误: {}", 
                        result.getBatchId(), result.getMessage());
                
                // 发送失败告警
                monitoringService.sendAlert("MONTHLY_BATCH_CLEANUP_FAILURE", 
                        "月度分批清理失败: " + result.getMessage(), "HIGH");
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            
            log.error("【定时分批清理】月度分批清理任务执行异常，耗时: {}ms", duration, e);
            
            // 发送异常告警
            monitoringService.sendAlert("MONTHLY_BATCH_CLEANUP_ERROR", 
                    "月度分批清理任务异常: " + e.getMessage(), "HIGH");
        }
    }

    /**
     * 执行第一阶段清理：3个月前成功邮件
     * 可通过管理接口单独调用
     */
    @Scheduled(fixedRate = Long.MAX_VALUE) // 不会自动执行，仅供手动调用
    public void executePhase1Only() {
        log.info("【阶段清理】开始执行第一阶段清理任务");
        
        try {
            IBatchEmailCleanupService.PhaseCleanupResult result = 
                    batchCleanupService.executePhase1Cleanup(1000);
                    
            log.info("【阶段清理】第一阶段清理完成 - 状态: {}, 处理邮件: {}, 成功: {}, 失败: {}", 
                    result.getStatus(), result.getTotalEmailsProcessed(), 
                    result.getSuccessCount(), result.getFailedCount());
                    
        } catch (Exception e) {
            log.error("【阶段清理】第一阶段清理执行异常", e);
        }
    }

    /**
     * 执行第二阶段清理：6个月前所有邮件
     * 可通过管理接口单独调用
     */
    @Scheduled(fixedRate = Long.MAX_VALUE) // 不会自动执行，仅供手动调用
    public void executePhase2Only() {
        log.info("【阶段清理】开始执行第二阶段清理任务");
        
        try {
            IBatchEmailCleanupService.PhaseCleanupResult result = 
                    batchCleanupService.executePhase2Cleanup(500);
                    
            log.info("【阶段清理】第二阶段清理完成 - 状态: {}, 处理邮件: {}, 成功: {}, 失败: {}", 
                    result.getStatus(), result.getTotalEmailsProcessed(), 
                    result.getSuccessCount(), result.getFailedCount());
                    
        } catch (Exception e) {
            log.error("【阶段清理】第二阶段清理执行异常", e);
        }
    }

    /**
     * 执行第三阶段清理：1年前历史邮件
     * 可通过管理接口单独调用
     */
    @Scheduled(fixedRate = Long.MAX_VALUE) // 不会自动执行，仅供手动调用
    public void executePhase3Only() {
        log.info("【阶段清理】开始执行第三阶段清理任务");
        
        try {
            IBatchEmailCleanupService.PhaseCleanupResult result = 
                    batchCleanupService.executePhase3Cleanup(200);
                    
            log.info("【阶段清理】第三阶段清理完成 - 状态: {}, 处理邮件: {}, 成功: {}, 失败: {}", 
                    result.getStatus(), result.getTotalEmailsProcessed(), 
                    result.getSuccessCount(), result.getFailedCount());
                    
        } catch (Exception e) {
            log.error("【阶段清理】第三阶段清理执行异常", e);
        }
    }

    /**
     * 手动触发完整分批清理任务（紧急情况或测试使用）
     */
    public void manualTriggerFullCleanup() {
        log.info("【手动触发】开始执行手动分批清理任务");
        executeScheduledBatchCleanup();
    }

    /**
     * 暂停当前执行的分批清理任务
     */
    public boolean pauseCurrentCleanup() {
        log.info("【任务控制】请求暂停当前分批清理任务");
        return batchCleanupService.pauseBatchCleanup();
    }

    /**
     * 恢复暂停的分批清理任务
     */
    public boolean resumeCurrentCleanup() {
        log.info("【任务控制】请求恢复分批清理任务");
        return batchCleanupService.resumeBatchCleanup();
    }

    /**
     * 获取当前分批清理状态
     */
    public IBatchEmailCleanupService.BatchCleanupStatus getCurrentStatus() {
        return batchCleanupService.getBatchCleanupStatus();
    }

    /**
     * 获取分批清理历史记录
     */
    public java.util.List<IBatchEmailCleanupService.BatchCleanupRecord> getCleanupHistory(int limit) {
        return batchCleanupService.getBatchCleanupHistory(limit);
    }
} 