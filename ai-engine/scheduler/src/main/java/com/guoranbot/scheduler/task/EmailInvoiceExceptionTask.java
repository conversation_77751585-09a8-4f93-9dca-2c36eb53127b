package com.guoranbot.scheduler.task;

import com.guoranbot.scheduler.service.IEmailParseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(name = "email.exception.task.enabled", havingValue = "true", matchIfMissing = true)
public class EmailInvoiceExceptionTask {

    @Autowired
    private IEmailParseService emailParseService;

    /**
     * 异常邮件处理定时任务
     * 调整频率：从每30分钟改为每2小时执行一次，减少系统负载
     */
    @Scheduled(cron = "0 0 0/2 * * *")
    public void parseExceptionEmail() {
        long startTime = System.currentTimeMillis();
        log.info("start to parse exception email");

        try {
            emailParseService.emailInvoiceException();
            log.info("end to parse exception email successfully, times:{}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("parse exception email failed, times:{}", System.currentTimeMillis() - startTime, e);
        }
    }
}
