package com.guoranbot.scheduler.task;

import com.guoranbot.scheduler.service.IEmailParseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(name = "email.parse.task.enabled", havingValue = "true", matchIfMissing = true)
public class EmailParseTask {

    @Autowired
    private IEmailParseService emailParseService;

    /**
     * 邮件解析定时任务
     * 保持每小时执行一次的频率
     */
    @Scheduled(cron = "0 0 0/1 * * *")
    public void parseEmail(){
        long startTime = System.currentTimeMillis();
        log.info("start to parse timeout email");

        try {
            emailParseService.parseEmail();
            log.info("end to parse timeout email successfully, times:{}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("parse timeout email failed, times:{}", System.currentTimeMillis() - startTime, e);
        }
    }
}
