package com.guoranbot.scheduler.util;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * ResponseEntity 兼容性工具类
 * 为不同版本的 Spring Boot 提供统一的 API
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class ResponseEntityUtil {

    /**
     * 创建内部服务器错误响应
     * 兼容 Spring Boot 2.2.x 版本（没有 internalServerError() 方法）
     * 
     * @return ResponseEntity.BodyBuilder
     */
    public static ResponseEntity.BodyBuilder internalServerError() {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 创建内部服务器错误响应（带响应体）
     * 
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> internalServerError(T body) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(body);
    }

    /**
     * 创建错误响应
     * 
     * @param status HTTP状态码
     * @return ResponseEntity.BodyBuilder
     */
    public static ResponseEntity.BodyBuilder status(HttpStatus status) {
        return ResponseEntity.status(status);
    }

    /**
     * 创建错误响应（带响应体）
     * 
     * @param status HTTP状态码
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> status(HttpStatus status, T body) {
        return ResponseEntity.status(status).body(body);
    }

    /**
     * 创建成功响应
     * 
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> ok(T body) {
        return ResponseEntity.ok(body);
    }

    /**
     * 创建无内容响应
     * 
     * @return ResponseEntity
     */
    public static ResponseEntity<Void> noContent() {
        return ResponseEntity.noContent().build();
    }

    /**
     * 创建未找到响应
     * 
     * @return ResponseEntity
     */
    public static ResponseEntity<Void> notFound() {
        return ResponseEntity.notFound().build();
    }

    /**
     * 创建未找到响应（带响应体）
     * 
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> notFound(T body) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(body);
    }

    /**
     * 创建错误请求响应
     * 
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> badRequest(T body) {
        return ResponseEntity.badRequest().body(body);
    }

    /**
     * 创建未授权响应
     * 
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> unauthorized(T body) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(body);
    }

    /**
     * 创建禁止访问响应
     * 
     * @param body 响应体
     * @param <T> 响应体类型
     * @return ResponseEntity
     */
    public static <T> ResponseEntity<T> forbidden(T body) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(body);
    }
}
