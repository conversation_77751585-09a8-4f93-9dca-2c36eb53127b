# 服务端口配置
server:
  port: ${SERVER_PORT:8767}

spring:
  application:
    name: scheduler-service
  profiles:
    active: dev
  cloud:
    nacos:
      config:
        shared-configs:
          - data-id: aifapiao-config.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: aifapiao-common-config.yaml
            group: DEFAULT_GROUP
            refresh: true
        server-addr: ${NACOS_ADDRESS:mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848}
        file-extension: yaml
        namespace: ${NACOS_NAMESPACE:af7cd89a-e994-4667-ba69-d95ebac4788a}
        accessKey: ${NACOS_ACCESSKEY:}
        secretKey: ${NACOS_SECRETKEY:}
      discovery:
        server-addr: ${NACOS_ADDRESS:mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848}
        auto-register: true
        namespace: ${NACOS_NAMESPACE:af7cd89a-e994-4667-ba69-d95ebac4788a}
        register:
          enabled: true
          group-name: ${NACOS_GROUP:DEFAULT_GROUP}
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true

# Feign配置优化
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: basic
      data-service:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: full
      email-service:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
      message-service:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
      pdfparser-service:
        connectTimeout: 2000
        readTimeout: 10000
        loggerLevel: basic

# 日志配置
logging:
  level:
    # Feign相关日志
    feign: INFO
    com.guoranbot.scheduler.service.feign: DEBUG

    # Spring相关日志
    org.springframework.cloud.openfeign: INFO
    org.springframework.context: WARN

    # Bean创建日志
    org.springframework.beans.factory: WARN

email:
  # 定时任务开关配置
  parse:
    task:
      enabled: true  # 邮件解析任务开关
  exception:
    task:
      enabled: true  # 异常邮件处理任务开关

  cleanup:
    # 缓存配置
    cache:
      enabled: true
      expire-time: 1800000  # 缓存过期时间 30分钟
      redis-expire-time: 3600  # Redis缓存过期时间 1小时

    monitoring:
      enabled: true
      # 告警阈值配置
      alerts:
        failure-rate-threshold: 0.05  # 失败率告警阈值 5%
        slow-execution-threshold: 900000  # 慢执行告警阈值 15分钟
        max-active-tasks: 3  # 最大并发任务数
        max-single-cleanup-emails: 10000  # 单次清理邮件数量上限
        check-interval-minutes: 5  # 告警检查间隔（分钟）
      # 数据保留策略
      retention:
        task-data-hours: 24  # 任务数据保留24小时
        alert-data-days: 7   # 告警数据保留7天
        metrics-data-days: 30  # 指标数据保留30天
      # 通知配置
      notification:
        enabled: true
        channels:
          - console  # 控制台输出
          - log      # 日志记录
          - email    # 邮件通知
          - dingtalk # 钉钉通知
          - sms      # 短信通知（紧急告警）
        # 通知人员配置
        recipients:
          email:
            - "<EMAIL>"
            - "<EMAIL>"
          dingtalk:
            webhook: "${DINGTALK_WEBHOOK_URL:}"
            secret: "${DINGTALK_SECRET:}"
            userIds:
              - "user001"  # 运维人员钉钉ID
              - "user002"  # 开发人员钉钉ID
          sms:
            phones:
              - "13800138000"  # 紧急联系人手机号
        # 告警级别通知策略
        severity-rules:
          CRITICAL:
            channels: ["dingtalk", "sms", "email"]
            immediate: true
          HIGH:
            channels: ["dingtalk", "email"]
            immediate: true
          MEDIUM:
            channels: ["dingtalk"]
            immediate: false
          LOW:
            channels: ["log"]
            immediate: false

# Spring Boot Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,httptrace,env,configprops
      base-path: /actuator
      path-mapping:
        prometheus: /metrics
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s  # 指标上报间隔
    tags:
      application: "aifapiao-email-cleanup"
      environment: "${SPRING_PROFILES_ACTIVE:dev}"
    # 自定义指标配置
    distribution:
      percentiles-histogram:
        "[email.cleanup.duration]": true
        "[email.cleanup.batch.size]": true
      percentiles:
        "[email.cleanup.duration]": 0.5, 0.95, 0.99
        "[email.cleanup.batch.size]": 0.5, 0.95, 0.99
      sla:
        "[email.cleanup.duration]": 100ms, 500ms, 1s, 5s, 10s, 30s, 60s
        "[email.cleanup.batch.size]": 10, 50, 100, 500, 1000, 5000, 10000

