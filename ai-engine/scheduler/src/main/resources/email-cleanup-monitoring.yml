# 邮箱清理监控配置
email:
  cleanup:
    monitoring:
      enabled: true
      # 告警阈值配置
      alerts:
        failure-rate-threshold: 0.05  # 失败率告警阈值 5%
        slow-execution-threshold: 900000  # 慢执行告警阈值 15分钟
        max-active-tasks: 3  # 最大并发任务数
        max-single-cleanup-emails: 10000  # 单次清理邮件数量上限
        check-interval-minutes: 5  # 告警检查间隔（分钟）
      # 数据保留策略
      retention:
        task-data-hours: 24  # 任务数据保留24小时
        alert-data-days: 7   # 告警数据保留7天
        metrics-data-days: 30  # 指标数据保留30天
      # 通知配置
      notification:
        enabled: true
        channels:
          - console  # 控制台输出
          - log      # 日志记录
          - email    # 邮件通知
          - dingtalk # 钉钉通知
          - sms      # 短信通知（紧急告警）
        # 通知人员配置
        recipients:
          email:
            - "<EMAIL>"
            - "<EMAIL>"
          dingtalk:
            webhook: "${DINGTALK_WEBHOOK_URL:}"
            secret: "${DINGTALK_SECRET:}"
            userIds:
              - "user001"  # 运维人员钉钉ID
              - "user002"  # 开发人员钉钉ID
          sms:
            phones:
              - "13800138000"  # 紧急联系人手机号
        # 告警级别通知策略
        severity-rules:
          CRITICAL:
            channels: ["dingtalk", "sms", "email"]
            immediate: true
          HIGH:
            channels: ["dingtalk", "email"]
            immediate: true
          MEDIUM:
            channels: ["dingtalk"]
            immediate: false
          LOW:
            channels: ["log"]
            immediate: false

# Spring Boot Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,httptrace,env,configprops
      base-path: /actuator
      path-mapping:
        prometheus: /metrics
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s  # 指标上报间隔
    tags:
      application: "aifapiao-email-cleanup"
      environment: "${SPRING_PROFILES_ACTIVE:dev}"
    # 自定义指标配置
    distribution:
      percentiles-histogram:
        "[email.cleanup.duration]": true
        "[email.cleanup.batch.size]": true
      percentiles:
        "[email.cleanup.duration]": 0.5, 0.95, 0.99
        "[email.cleanup.batch.size]": 0.5, 0.95, 0.99
      sla:
        "[email.cleanup.duration]": 100ms, 500ms, 1s, 5s, 10s, 30s, 60s
        "[email.cleanup.batch.size]": 10, 50, 100, 500, 1000, 5000, 10000

# 日志配置
logging:
  level:
    com.guoranbot.scheduler.service.impl.EmailCleanupMonitoringServiceImpl: INFO
    com.guoranbot.scheduler.service.impl.NotificationServiceImpl: INFO
    com.guoranbot.scheduler.task: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
