<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱清理监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
        }
        
        .metric-unit {
            font-size: 16px;
            color: #999;
            margin-left: 5px;
        }
        
        .status-healthy { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-critical { color: #F44336; }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .alerts-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .alert-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .alert-critical { 
            background-color: #ffebee; 
            border-left-color: #f44336; 
        }
        
        .alert-high { 
            background-color: #fff3e0; 
            border-left-color: #ff9800; 
        }
        
        .alert-medium { 
            background-color: #e8f5e8; 
            border-left-color: #4caf50; 
        }
        
        .controls-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-danger {
            background: #f44336;
        }
        
        .btn-danger:hover {
            background: #d32f2f;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .timestamp {
            font-size: 12px;
            color: #999;
            text-align: right;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>邮箱清理监控仪表板</h1>
        <p>实时监控邮箱清理任务的执行状态和性能指标</p>
    </div>

    <div class="container">
        <!-- 实时指标 -->
        <div class="metrics-grid" id="metricsGrid">
            <div class="loading">正在加载监控数据...</div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-card">
                <h3>成功率趋势</h3>
                <div id="successRateChart" style="height: 200px; display: flex; align-items: center; justify-content: center; color: #666;">
                    图表功能需要集成Chart.js或其他图表库
                </div>
            </div>
            <div class="chart-card">
                <h3>执行时间趋势</h3>
                <div id="durationChart" style="height: 200px; display: flex; align-items: center; justify-content: center; color: #666;">
                    图表功能需要集成Chart.js或其他图表库
                </div>
            </div>
        </div>

        <!-- 告警信息 -->
        <div class="alerts-section">
            <h3>活跃告警</h3>
            <div id="alertsList">
                <div class="loading">正在加载告警信息...</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <h3>操作控制</h3>
            <button class="btn" onclick="refreshData()">刷新数据</button>
            <button class="btn" onclick="testNotification('dingtalk')">测试钉钉通知</button>
            <button class="btn" onclick="testNotification('email')">测试邮件通知</button>
            <button class="btn btn-danger" onclick="sendTestAlert()">发送测试告警</button>
            
            <div id="operationResult"></div>
        </div>

        <div class="timestamp" id="lastUpdate"></div>
    </div>

    <script>
        // 全局变量
        let refreshInterval;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            startAutoRefresh();
        });
        
        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                const response = await fetch('/monitoring/api/realtime');
                const data = await response.json();
                
                if (data.error) {
                    showError('获取数据失败: ' + data.error);
                    return;
                }
                
                updateMetrics(data.metrics);
                updateAlerts(data.alerts);
                updateTimestamp(data.timestamp);
                
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('网络请求失败: ' + error.message);
            }
        }
        
        // 更新指标显示
        function updateMetrics(metrics) {
            const metricsGrid = document.getElementById('metricsGrid');
            
            const metricsHtml = `
                <div class="metric-card">
                    <div class="metric-title">总任务数</div>
                    <div class="metric-value">${metrics.totalTasksExecuted || 0}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">成功率</div>
                    <div class="metric-value ${getStatusClass(metrics.successRate)}">${((metrics.successRate || 0) * 100).toFixed(1)}<span class="metric-unit">%</span></div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">当前活跃任务</div>
                    <div class="metric-value ${metrics.currentActiveTasks > 3 ? 'status-warning' : 'status-healthy'}">${metrics.currentActiveTasks || 0}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">已处理邮件</div>
                    <div class="metric-value">${metrics.totalEmailsProcessed || 0}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">平均执行时间</div>
                    <div class="metric-value">${((metrics.averageDuration || 0) / 1000).toFixed(1)}<span class="metric-unit">秒</span></div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">IMAP错误</div>
                    <div class="metric-value ${metrics.totalImapErrors > 0 ? 'status-critical' : 'status-healthy'}">${metrics.totalImapErrors || 0}</div>
                </div>
            `;
            
            metricsGrid.innerHTML = metricsHtml;
        }
        
        // 更新告警显示
        function updateAlerts(alerts) {
            const alertsList = document.getElementById('alertsList');
            
            if (!alerts.activeAlerts || alerts.activeAlerts.length === 0) {
                alertsList.innerHTML = '<div style="color: #4CAF50; text-align: center; padding: 20px;">✓ 当前无活跃告警</div>';
                return;
            }
            
            const alertsHtml = alerts.activeAlerts.map(alert => `
                <div class="alert-item alert-${alert.severity.toLowerCase()}">
                    <strong>${alert.type}</strong>: ${alert.message}
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        ${new Date(alert.timestamp).toLocaleString()}
                    </div>
                </div>
            `).join('');
            
            alertsList.innerHTML = alertsHtml;
        }
        
        // 获取状态样式类
        function getStatusClass(successRate) {
            if (successRate >= 0.95) return 'status-healthy';
            if (successRate >= 0.9) return 'status-warning';
            return 'status-critical';
        }
        
        // 更新时间戳
        function updateTimestamp(timestamp) {
            const lastUpdate = document.getElementById('lastUpdate');
            lastUpdate.textContent = '最后更新: ' + new Date(timestamp).toLocaleString();
        }
        
        // 显示错误信息
        function showError(message) {
            const metricsGrid = document.getElementById('metricsGrid');
            metricsGrid.innerHTML = `<div class="error">${message}</div>`;
        }
        
        // 显示操作结果
        function showOperationResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('operationResult');
            resultDiv.innerHTML = `<div class="${isSuccess ? 'success' : 'error'}">${message}</div>`;
            setTimeout(() => {
                resultDiv.innerHTML = '';
            }, 5000);
        }
        
        // 刷新数据
        function refreshData() {
            loadDashboardData();
        }
        
        // 开始自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(loadDashboardData, 30000); // 30秒刷新一次
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        // 测试通知
        async function testNotification(channel) {
            try {
                const response = await fetch('/monitoring/api/test-notification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `channel=${channel}&message=监控系统测试消息`
                });
                
                const result = await response.json();
                showOperationResult(result.message, result.success);
                
            } catch (error) {
                showOperationResult('测试通知失败: ' + error.message, false);
            }
        }
        
        // 发送测试告警
        async function sendTestAlert() {
            try {
                const response = await fetch('/monitoring/api/send-alert', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'alertType=TEST_ALERT&message=这是一个测试告警&severity=MEDIUM'
                });
                
                const result = await response.json();
                showOperationResult(result.message, result.success);
                
                // 刷新数据以显示新的告警
                setTimeout(loadDashboardData, 1000);
                
            } catch (error) {
                showOperationResult('发送测试告警失败: ' + error.message, false);
            }
        }
        
        // 页面卸载时停止自动刷新
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>
