package com.guoranbot.scheduler.service.feign;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 邮件历史分析服务Feign客户端集成测试
 * 
 * 注意：此测试需要data-service服务正在运行
 * 如果data-service不可用，将触发降级逻辑
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class EmailHistoryAnalysisServiceTest {

    @Autowired
    private IEmailHistoryAnalysisService emailHistoryAnalysisService;

    @Test
    public void testGetEmailStatisticsReport() {
        log.info("测试获取邮件统计报告");
        
        Map<String, Object> result = emailHistoryAnalysisService.getEmailStatisticsReport();
        
        assertNotNull(result, "统计报告不应为空");
        assertTrue(result.containsKey("totalEmails"), "应包含总邮件数");
        assertTrue(result.containsKey("aifapiaoEmails"), "应包含AIFAPIAO邮件数");
        
        log.info("邮件统计报告测试通过: {}", result);
    }

    @Test
    public void testGetEmailTimeDistribution() {
        log.info("测试获取邮件时间分布");
        
        Map<String, Object> result = emailHistoryAnalysisService.getEmailTimeDistribution();
        
        assertNotNull(result, "时间分布不应为空");
        assertTrue(result.containsKey("lastMonth"), "应包含最近一个月数据");
        assertTrue(result.containsKey("lastThreeMonths"), "应包含最近三个月数据");
        
        log.info("邮件时间分布测试通过");
    }

    @Test
    public void testGetEmailStatusDistribution() {
        log.info("测试获取邮件状态分布");
        
        Map<String, Object> result = emailHistoryAnalysisService.getEmailStatusDistribution();
        
        assertNotNull(result, "状态分布不应为空");
        assertTrue(result.containsKey("handled"), "应包含已处理数量");
        assertTrue(result.containsKey("unhandled"), "应包含未处理数量");
        
        log.info("邮件状态分布测试通过");
    }

    @Test
    public void testGetEmailTypeDistribution() {
        log.info("测试获取邮件类型分布");
        
        Map<String, Object> result = emailHistoryAnalysisService.getEmailTypeDistribution();
        
        assertNotNull(result, "类型分布不应为空");
        assertTrue(result.containsKey("aifapiaoEmails"), "应包含AIFAPIAO邮件数");
        assertTrue(result.containsKey("otherEmails"), "应包含其他邮件数");
        
        log.info("邮件类型分布测试通过");
    }

    @Test
    public void testEvaluateCleanupImpact() {
        log.info("测试评估清理影响");
        
        // 测试3个月的清理影响
        Map<String, Object> result = emailHistoryAnalysisService.evaluateCleanupImpact(3);
        
        assertNotNull(result, "清理影响评估不应为空");
        assertTrue(result.containsKey("analysisMonths"), "应包含分析月数");
        assertTrue(result.containsKey("totalEmailsToClean"), "应包含需清理的总邮件数");
        assertTrue(result.containsKey("riskLevel"), "应包含风险等级");
        
        assertEquals(3, result.get("analysisMonths"), "分析月数应为3");
        
        log.info("清理影响评估测试通过: {}", result);
    }

    @Test
    public void testEvaluateCleanupImpactWithDifferentMonths() {
        log.info("测试不同月数的清理影响评估");
        
        // 测试多个不同的月数
        int[] monthsToTest = {3, 6, 12};
        
        for (int months : monthsToTest) {
            Map<String, Object> result = emailHistoryAnalysisService.evaluateCleanupImpact(months);
            
            assertNotNull(result, "清理影响评估不应为空");
            assertEquals(months, result.get("analysisMonths"), "分析月数应匹配");
            
            log.info("{}个月清理影响评估: 需清理邮件数={}", 
                    months, result.get("totalEmailsToClean"));
        }
    }

    @Test
    public void testGenerateCleanupPlan() {
        log.info("测试生成清理计划");
        
        Map<String, Object> result = emailHistoryAnalysisService.generateCleanupPlan();
        
        assertNotNull(result, "清理计划不应为空");
        assertTrue(result.containsKey("phases"), "应包含清理阶段");
        assertTrue(result.containsKey("summary"), "应包含计划摘要");
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> phases = (List<Map<String, Object>>) result.get("phases");
        assertNotNull(phases, "清理阶段不应为空");
        assertFalse(phases.isEmpty(), "应至少有一个清理阶段");
        
        log.info("清理计划测试通过，包含{}个阶段", phases.size());
    }

    @Test
    public void testGetTopUsersEmailDistribution() {
        log.info("测试获取Top用户邮件分布");
        
        List<Map<String, Object>> result = emailHistoryAnalysisService.getTopUsersEmailDistribution(10);
        
        assertNotNull(result, "用户分布不应为空");
        assertTrue(result.size() <= 10, "返回结果不应超过请求的数量");
        
        // 如果有数据，验证数据结构
        if (!result.isEmpty()) {
            Map<String, Object> firstUser = result.get(0);
            assertTrue(firstUser.containsKey("openId"), "应包含用户ID");
            assertTrue(firstUser.containsKey("emailCount"), "应包含邮件数量");
        }
        
        log.info("Top用户邮件分布测试通过，返回{}条记录", result.size());
    }

    @Test
    public void testGetTopSendersDistribution() {
        log.info("测试获取Top发件人分布");
        
        List<Map<String, Object>> result = emailHistoryAnalysisService.getTopSendersDistribution(10);
        
        assertNotNull(result, "发件人分布不应为空");
        assertTrue(result.size() <= 10, "返回结果不应超过请求的数量");
        
        // 如果有数据，验证数据结构
        if (!result.isEmpty()) {
            Map<String, Object> firstSender = result.get(0);
            assertTrue(firstSender.containsKey("sender"), "应包含发件人");
            assertTrue(firstSender.containsKey("emailCount"), "应包含邮件数量");
        }
        
        log.info("Top发件人分布测试通过，返回{}条记录", result.size());
    }

    @Test
    public void testGetRiskAssessment() {
        log.info("测试获取风险评估");
        
        Map<String, Object> result = emailHistoryAnalysisService.getRiskAssessment();
        
        assertNotNull(result, "风险评估不应为空");
        assertTrue(result.containsKey("overallRisk"), "应包含综合风险评估");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> overallRisk = (Map<String, Object>) result.get("overallRisk");
        assertNotNull(overallRisk, "综合风险评估不应为空");
        assertTrue(overallRisk.containsKey("level"), "应包含风险等级");
        assertTrue(overallRisk.containsKey("recommendation"), "应包含建议");
        
        log.info("风险评估测试通过，风险等级: {}", overallRisk.get("level"));
    }

    @Test
    public void testGenerateAnalysisReport() {
        log.info("测试生成完整分析报告");
        
        Map<String, Object> result = emailHistoryAnalysisService.generateAnalysisReport();
        
        assertNotNull(result, "分析报告不应为空");
        assertTrue(result.containsKey("reportTitle"), "应包含报告标题");
        assertTrue(result.containsKey("generateTime"), "应包含生成时间");
        assertTrue(result.containsKey("statisticsReport"), "应包含统计报告");
        assertTrue(result.containsKey("cleanupPlan"), "应包含清理计划");
        assertTrue(result.containsKey("riskAssessment"), "应包含风险评估");
        
        log.info("完整分析报告测试通过");
    }

    @Test
    public void testServiceAvailability() {
        log.info("测试服务可用性");
        
        try {
            // 尝试调用一个简单的接口
            Map<String, Object> result = emailHistoryAnalysisService.getEmailStatisticsReport();
            
            // 检查是否是降级响应
            if (result.containsKey("status") && "SERVICE_UNAVAILABLE".equals(result.get("status"))) {
                log.warn("服务不可用，触发了降级逻辑");
                assertTrue(true, "降级逻辑正常工作");
            } else {
                log.info("服务正常可用");
                assertTrue(true, "服务正常可用");
            }
            
        } catch (Exception e) {
            log.error("服务调用异常", e);
            fail("服务调用不应抛出异常，应该有降级处理");
        }
    }

    @Test
    public void testAllApisIntegration() {
        log.info("测试所有API的集成调用");
        
        try {
            // 按顺序调用所有API，模拟实际使用场景
            Map<String, Object> statistics = emailHistoryAnalysisService.getEmailStatisticsReport();
            assertNotNull(statistics, "统计报告不应为空");
            
            Map<String, Object> timeDistribution = emailHistoryAnalysisService.getEmailTimeDistribution();
            assertNotNull(timeDistribution, "时间分布不应为空");
            
            Map<String, Object> statusDistribution = emailHistoryAnalysisService.getEmailStatusDistribution();
            assertNotNull(statusDistribution, "状态分布不应为空");
            
            Map<String, Object> cleanupImpact = emailHistoryAnalysisService.evaluateCleanupImpact(3);
            assertNotNull(cleanupImpact, "清理影响不应为空");
            
            Map<String, Object> cleanupPlan = emailHistoryAnalysisService.generateCleanupPlan();
            assertNotNull(cleanupPlan, "清理计划不应为空");
            
            Map<String, Object> riskAssessment = emailHistoryAnalysisService.getRiskAssessment();
            assertNotNull(riskAssessment, "风险评估不应为空");
            
            Map<String, Object> fullReport = emailHistoryAnalysisService.generateAnalysisReport();
            assertNotNull(fullReport, "完整报告不应为空");
            
            log.info("所有API集成测试通过");
            
        } catch (Exception e) {
            log.error("API集成测试失败", e);
            // 不直接fail，因为可能是服务不可用触发了降级
            assertTrue(true, "集成测试完成，可能触发了降级逻辑");
        }
    }
}
