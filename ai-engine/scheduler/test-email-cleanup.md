# 邮件清理服务404错误测试和验证指南（Nacos版本）

## 问题描述
scheduler-service调用data-service的`getAifapiaoEmailAccounts`接口时返回404错误，导致邮件清理失败。
项目使用Nacos作为服务发现和配置中心。

## 修复内容
1. 改进了Feign客户端配置，添加了重试机制和错误处理
2. 创建了Fallback实现，提供服务降级功能
3. 添加了详细的错误日志和调试信息
4. 实现了服务健康检查功能
5. 创建了Nacos服务发现检查工具
6. 创建了诊断工具，帮助快速定位问题

## 测试步骤

### 1. Nacos服务发现检查
```bash
# 检查Nacos中的服务注册状态
curl -X GET "http://localhost:8767/health/nacos"

# 检查整体健康状态
curl -X GET "http://localhost:8767/health/overall"

# 检查Data Service健康状态
curl -X GET "http://localhost:8767/health/data-service"

# 检查AIFAPIAO邮箱账户接口健康状态
curl -X GET "http://localhost:8767/health/aifapiao-accounts"

# 获取完整健康报告
curl -X GET "http://localhost:8767/health/report"
```

### 2. 运行诊断检查
```bash
# 运行完整诊断
curl -X GET "http://localhost:8767/health/diagnostic"
```

### 3. 测试邮件清理功能
```bash
# 手动触发成功邮件清理
curl -X POST "http://localhost:8767/email-cleanup/success?days=1"

# 手动触发失败邮件清理
curl -X POST "http://localhost:8767/email-cleanup/failed?days=3"

# 清理指定邮箱
curl -X POST "http://localhost:8767/email-cleanup/email?emailAddress=<EMAIL>&days=1&isSuccessOnly=true"
```

### 4. 检查日志
查看scheduler-service日志，关注以下内容：
- Feign客户端连接日志
- 服务发现日志
- 错误详细信息
- 重试机制执行情况

## 预期结果

### 成功场景
- 健康检查返回true
- 诊断报告显示所有检查通过
- 邮件清理正常执行
- 日志显示成功获取AIFAPIAO邮箱账户

### 失败场景处理
- 404错误时，Fallback机制生效
- 详细错误日志帮助定位问题
- 重试机制自动重试失败的请求
- 诊断工具提供修复建议

## 故障排除

### 如果仍然出现404错误
1. 检查data-service是否正常运行
2. 验证服务注册状态：访问Nacos控制台
3. 检查网络连接：ping data-service实例
4. 验证接口路径：确认`/email/cleanup/aifapiao-accounts`存在

### 如果Nacos服务发现失败
1. **检查Nacos服务器状态**
   ```bash
   # 访问Nacos控制台
   http://mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848/nacos
   ```

2. **验证服务注册配置**
   - 检查命名空间：`af7cd89a-e994-4667-ba69-d95ebac4788a`
   - 检查组配置：`DEFAULT_GROUP`
   - 验证服务名称：`data-service`

3. **检查网络连接**
   ```bash
   # 测试Nacos连接
   telnet mse-30e0a982-p.nacos-ans.mse.aliyuncs.com 8848
   ```

4. **查看服务实例**
   - 在Nacos控制台查看`data-service`是否有健康实例
   - 检查实例的IP和端口是否正确

### 如果获取不到邮箱账户
1. 检查数据库中是否有AIFAPIAO邮箱用户
2. 验证AccountUserService实现
3. 检查数据库连接

## 监控和告警

### 关键指标
- 服务健康状态
- 接口调用成功率
- 响应时间
- 错误率

### 日志关键字
- "Data service unavailable"
- "404 reading IDataInfoService"
- "Cannot find account info for email"
- "Service health check failed"

## 长期改进建议

1. **监控告警**：设置服务健康状态监控告警
2. **自动恢复**：实现服务故障自动恢复机制
3. **配置中心**：将Feign配置迁移到Nacos配置中心
4. **链路追踪**：添加分布式链路追踪，便于问题定位
5. **熔断器**：添加Hystrix熔断器，防止级联故障
