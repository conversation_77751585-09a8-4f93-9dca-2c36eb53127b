# 简单验证脚本
Write-Host "=== 邮件历史分析服务验证 ===" -ForegroundColor Green

# 检查关键文件
$files = @(
    "src/main/java/com/guoranbot/scheduler/service/feign/IEmailHistoryAnalysisService.java",
    "src/main/java/com/guoranbot/scheduler/controller/EmailHistoryAnalysisTestController.java",
    "src/test/java/com/guoranbot/scheduler/service/feign/EmailHistoryAnalysisServiceTest.java"
)

Write-Host "检查文件存在性:" -ForegroundColor Yellow
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  OK: $file" -ForegroundColor Green
    } else {
        Write-Host "  MISSING: $file" -ForegroundColor Red
    }
}

Write-Host "`n验证完成!" -ForegroundColor Green
Write-Host "所有关键文件已创建，可以进行部署测试。" -ForegroundColor Cyan
