#!/bin/bash

# 显示批量构建计划，不实际构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 批量构建计划 ===${NC}"

# 复制 build-with-jib.sh 中的阶段定义
phase1=("common" "oss" "image")
phase2=("eureka" "datasource" "email-parser" "pdf-parser" "enterprise-info")
phase3=("analysis" "classify" "email" "message" "scheduler")
phase4=("sms" "card-import" "dasource-listen" "customize-email-datasource")
phase5=("toc-web" "client/didi-client" "client/ekb-client" "client/mini-program")

all_phases=("phase1" "phase2" "phase3" "phase4" "phase5")

total_services=0
mini_program_found=false

for phase in "${all_phases[@]}"; do
    eval "services=(\${$phase[@]})"
    echo -e "${YELLOW}构建阶段 $phase: ${services[*]}${NC}"
    
    for service in "${services[@]}"; do
        if [[ -d "$service" ]]; then
            echo -e "${GREEN}  ✓ 将构建: $service${NC}"
            ((total_services++))
            if [[ "$service" == "client/mini-program" ]]; then
                echo -e "${BLUE}    🎯 找到 mini-program 服务！${NC}"
                mini_program_found=true
            fi
        else
            echo -e "${RED}  ❌ 跳过（目录不存在）: $service${NC}"
            if [[ "$service" == "client/mini-program" ]]; then
                echo -e "${RED}    ⚠️  mini-program 被跳过！${NC}"
            fi
        fi
    done
    echo ""
done

echo -e "${BLUE}=== 构建计划总结 ===${NC}"
echo -e "${GREEN}总共将构建 $total_services 个服务${NC}"

if [[ "$mini_program_found" == "true" ]]; then
    echo -e "${GREEN}✅ client/mini-program 包含在构建计划中${NC}"
else
    echo -e "${RED}❌ client/mini-program 不在构建计划中${NC}"
fi

echo ""
echo -e "${BLUE}如果您在实际批量构建中没有看到 mini-program，可能的原因：${NC}"
echo -e "${YELLOW}1. 前面阶段的构建失败，导致后续阶段被跳过${NC}"
echo -e "${YELLOW}2. 构建过程中出现错误，但没有显示详细信息${NC}"
echo -e "${YELLOW}3. 构建脚本的某些条件检查失败${NC}"
echo ""
echo -e "${BLUE}建议：${NC}"
echo -e "${GREEN}1. 单独构建 mini-program: ./build-with-jib.sh client/mini-program${NC}"
echo -e "${GREEN}2. 查看完整的批量构建日志${NC}"
echo -e "${GREEN}3. 检查前面阶段是否有构建失败${NC}"
