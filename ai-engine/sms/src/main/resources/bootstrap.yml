# 服务端口配置
server:
  port: ${SERVER_PORT:8770}

spring:
  profiles:
    active: dev
  application:
    name: sms-service
  cloud:
    nacos:
      config:
        shared-configs:
          - data-id: aifapiao-config.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: aifapiao-common-config.yaml
            group: DEFAULT_GROUP
            refresh: true
        server-addr: ${NACOS_ADDRESS:mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848}
        file-extension: yaml
        namespace: ${NACOS_NAMESPACE:af7cd89a-e994-4667-ba69-d95ebac4788a}
        accessKey: ${NACOS_ACCESSKEY:}
        secretKey: ${NACOS_SECRETKEY:}
      discovery:
        server-addr: ${NACOS_ADDRESS:mse-30e0a982-p.nacos-ans.mse.aliyuncs.com:8848}
        auto-register: true
        namespace: ${NACOS_NAMESPACE:af7cd89a-e994-4667-ba69-d95ebac4788a}
        register:
          enabled: true
          group-name: ${NACOS_GROUP:DEFAULT_GROUP}

