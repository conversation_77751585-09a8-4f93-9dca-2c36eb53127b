#!/bin/bash

# 测试批量构建是否包含 mini-program 服务
# 模拟 build-with-jib.sh 的批量构建逻辑

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 测试批量构建逻辑 ===${NC}"

# 复制 build-with-jib.sh 中的服务列表和阶段定义
ALL_SERVICES=(
    "analysis"
    "classify"
    "datasource"
    "email-parser"
    "pdf-parser"
    "enterprise-info"
    "email"
    "message"
    "scheduler"
    "card-import"
    "sms"
    "dasource-listen"
    "customize-email-datasource"
    "toc-web"
    "client/didi-client"
    "client/ekb-client"
    "client/mini-program"
)

echo -e "${YELLOW}所有服务列表:${NC}"
printf "  %s\n" "${ALL_SERVICES[@]}"
echo ""

# 分阶段构建逻辑
phase1=("common" "oss" "image")
phase2=("eureka" "datasource" "email-parser" "pdf-parser" "enterprise-info")
phase3=("analysis" "classify" "email" "message" "scheduler")
phase4=("sms" "card-import" "dasource-listen" "customize-email-datasource")
phase5=("toc-web" "client/didi-client" "client/ekb-client" "client/mini-program")

all_phases=("phase1" "phase2" "phase3" "phase4" "phase5")

echo -e "${BLUE}=== 分阶段构建测试 ===${NC}"

for phase in "${all_phases[@]}"; do
    eval "services=(\${$phase[@]})"
    echo -e "${YELLOW}构建阶段 $phase: ${services[*]}${NC}"
    
    for service in "${services[@]}"; do
        if [[ -d "$service" ]]; then
            echo -e "${GREEN}  ✓ 目录存在: $service${NC}"
            if [[ "$service" == "client/mini-program" ]]; then
                echo -e "${BLUE}    🎯 找到 mini-program 服务！${NC}"
            fi
        else
            echo -e "${RED}  ❌ 目录不存在: $service${NC}"
            if [[ "$service" == "client/mini-program" ]]; then
                echo -e "${RED}    ⚠️  mini-program 目录不存在，会被跳过！${NC}"
            fi
        fi
    done
    echo ""
done

# 检查 mini-program 是否在服务列表中
echo -e "${BLUE}=== mini-program 服务检查 ===${NC}"
if [[ " ${ALL_SERVICES[*]} " =~ " client/mini-program " ]]; then
    echo -e "${GREEN}✓ client/mini-program 在 ALL_SERVICES 列表中${NC}"
else
    echo -e "${RED}❌ client/mini-program 不在 ALL_SERVICES 列表中${NC}"
fi

# 检查目录是否存在
if [[ -d "client/mini-program" ]]; then
    echo -e "${GREEN}✓ client/mini-program 目录存在${NC}"
    echo -e "${BLUE}  目录内容:${NC}"
    ls -la "client/mini-program/" | head -5
else
    echo -e "${RED}❌ client/mini-program 目录不存在${NC}"
    echo -e "${YELLOW}  这就是为什么批量构建时会跳过它的原因！${NC}"
fi

echo -e "${BLUE}=== 测试完成 ===${NC}"
