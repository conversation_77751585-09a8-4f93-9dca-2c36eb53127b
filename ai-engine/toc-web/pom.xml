<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0">
        <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.guoranbot</groupId>
		<artifactId>ai-engine</artifactId>
		<version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
	</parent>
    <groupId>com.guoranbot</groupId>
    <artifactId>toc-web</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>toc-web</name>
    <description>Technoical Operation Center Web</description>
	<properties>
        <java.version>11</java.version>
        <spring-cloud.version>Hoxton.SR3</spring-cloud.version>
        <!-- 使用bootstrap.yml中的application.name作为镜像�?-->
        <docker.image.name>toc-web</docker.image.name>
	</properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>-->
<!--        </dependency>-->
        <!-- Nacos�����ֺ��������� -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.guoranbot</groupId>
            <artifactId>common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.guoranbot</groupId>
            <artifactId>oss</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
            
            <!-- Jib插件 -->
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <!-- 容器特定配置 -->
                    <container>
                        <ports>
                            <port>8772</port>
                        </ports>
                        <jvmFlags>
                            <jvmFlag>-server</jvmFlag>
                            <jvmFlag>-Xms512m</jvmFlag>
                            <jvmFlag>-Xmx1024m</jvmFlag>
                            <jvmFlag>-XX:+UseG1GC</jvmFlag>
                            <jvmFlag>-XX:MaxGCPauseMillis=100</jvmFlag>
                            <jvmFlag>-XX:+UseStringDeduplication</jvmFlag>
                            <jvmFlag>-Djava.security.egd=file:/dev/./urandom</jvmFlag>
                            <jvmFlag>-Duser.timezone=Asia/Shanghai</jvmFlag>
                            <jvmFlag>-javaagent:/dd-java-agent-v1.14.0-guance.jar</jvmFlag>
                        </jvmFlags>
                        <environment>
                            <TZ>Asia/Shanghai</TZ>
                            <DD_AGENT_HOST>datadog-agent</DD_AGENT_HOST>
                            <DD_SERVICE>toc-web</DD_SERVICE>
                            <DD_ENV>production</DD_ENV>
                        </environment>
                        <mainClass>com.guoranbot.tocweb.TocWebApplication</mainClass>
                    </container>
                    <!-- APM Agent 文件复制 -->
                    <extraDirectories>
                        <paths>
                            <path>
                                <from>./</from>
                                <includes>dd-java-agent-v1.14.0-guance.jar</includes>
                                <into>/</into>
                            </path>
                        </paths>
                    </extraDirectories>
                </configuration>
            </plugin>
<!--            <plugin>-->

<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>dockerfile-maven-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default</id>-->
<!--                        <goals>-->
<!--                            &lt;!&ndash;如果package时不想用docker打包,就注释掉这个 goal build &ndash;&gt;-->
<!--                            &lt;!&ndash;  <goal>build</goal>-->
<!--                             <goal>push</goal> &ndash;&gt;-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->

<!--                <configuration>-->

<!--                    <username>${docker.username}</username>-->
<!--                    <password>${docker.password}</password>-->
<!--                    <dockerfile>${pom.basedir}/Dockerfile</dockerfile>-->
<!--                    <repository>${docker.repostory}/xbox/${project.artifactId}</repository>-->
<!--                    <buildArgs>-->
<!--                        &lt;!&ndash;提供参数向Dockerfile传�?ndash;&gt;-->
<!--                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>-->
<!--                    </buildArgs>-->
<!--                    <tag>latest</tag>-->

<!--                </configuration>-->
<!--            </plugin>-->
		</plugins>
	</build>
</project>

